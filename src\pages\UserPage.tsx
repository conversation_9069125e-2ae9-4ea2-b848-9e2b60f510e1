import React, { useEffect, useState, useCallback } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Avatar,
  Button,
  Card,
  CardBody,
  Badge,
  useColorMode,
  useToast,
  Spinner,
  Center,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Image,
  SimpleGrid,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  TagLeftIcon,
  Skeleton,
  SkeletonText,
  IconButton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { FaUser, FaBookmark, FaHeart, FaSignInAlt, FaGoogle, FaComment, FaClock, FaEye, FaTags, FaTrash } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import useUserStore from '../store/useUserStore';
import useFavoriteBookmarkStore from '../store/useFollowBookmarkStore';
// import { useAuth } from '../hooks/useAuthNew'; // REMOVED - causing API spam
import LoginButton from '../components/Auth/LoginButton';
import GoogleStyleLoginButton from '../components/Auth/GoogleStyleLoginButton';
import { blogConfig } from '../config';
import { commentService } from '../services/commentService';
import { getHistoryData } from '../utils/indexedDBUtils';

const UserPage: React.FC = () => {
  const { colorMode } = useColorMode();
  const toast = useToast();
  const navigate = useNavigate();

  // Use store data directly instead of auth hooks to avoid duplicate userinfo calls
  const {
    isAuthenticated,
    user,
    userId,
    accessToken,
    storeReady
  } = useUserStore();

  // No auth hooks to avoid duplicate API calls

  const {
    bookmarks,
    favorites,
    initialize: initializeStore
  } = useFavoriteBookmarkStore();

  // Local state
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  // Check if we're still determining auth state
  const isAuthLoading = !storeReady || (storeReady && !isAuthenticated && !user && isLoading);
  const [recentComments, setRecentComments] = useState<any[]>([]);
  const [favoritesPosts, setFavoritesPosts] = useState<any[]>([]);
  const [bookmarkedPosts, setBookmarkedPosts] = useState<any[]>([]);
  const [loadingComments, setLoadingComments] = useState(false);
  const [loadingFavorites, setLoadingFavorites] = useState(false);
  const [loadingBookmarks, setLoadingBookmarks] = useState(false);
  const [deletingCommentId, setDeletingCommentId] = useState<string | null>(null);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Delete comment modal
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const [commentToDelete, setCommentToDelete] = useState<any>(null);

  // Theme colors
  const isDark = colorMode === 'dark';
  const bgColor = isDark ? '#000000' : '#ffffff';
  const cardBg = isDark ? '#131313' : '#ffffff';
  const textColor = isDark ? '#ffffff' : '#000000';
  const mutedColor = isDark ? '#cccccc' : '#666666';
  const borderColor = isDark ? '#333333' : '#e5e5e5';
  const accentColor = '#00d4ff';

  // Fetch recent comments using Blogger API with pagination
  const fetchRecentComments = async () => {
    if (!user?.email || !accessToken) {
      console.log('🔍 Missing user email or access token:', { hasUser: !!user?.email, hasToken: !!accessToken });
      return;
    }

    if (loadingComments) {
      console.log('🔄 Already loading comments, skipping...');
      return;
    }

    setLoadingComments(true);
    try {
      console.log('🔍 Fetching user comments via Blogger API with pagination...');
      console.log('🔍 Current user info:', { name: user.name, email: user.email, sub: user.sub });

      let userComments: any[] = [];
      let profileData: any = null;

      // Get user's Blogger profile first
      try {
        const profileResponse = await fetch(
          `https://www.googleapis.com/blogger/v3/users/self?access_token=${accessToken}`
        );

        if (profileResponse.ok) {
          profileData = await profileResponse.json();
          console.log('🔍 User profile data:', profileData);
        }
      } catch (profileError) {
        console.error('Error fetching user profile:', profileError);
      }

      // Fetch comments from first page only (no pagination to prevent spam)
      const url = `https://www.googleapis.com/blogger/v3/blogs/${blogConfig.blogId}/comments?access_token=${accessToken}&maxResults=50&orderBy=published&status=live`;

      console.log('🔍 Fetching comments (single page only)...');

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('🔍 Comments response:', {
        items: data.items?.length || 0,
        totalItems: data.totalItems
      });

      const allComments = data.items || [];

      // Filter comments for current user
      userComments = allComments.filter((comment: any) => {
        const authorId = comment.author?.id;
        const authorUrl = comment.author?.url?.toLowerCase() || '';
        const authorName = comment.author?.displayName?.toLowerCase() || '';
        const userName = user.name?.toLowerCase() || '';
        const userEmail = user.email?.toLowerCase() || '';

        // Multiple matching criteria
        const idMatch = profileData?.id && authorId === profileData.id;
        const nameMatch = authorName === userName;
        const emailInUrl = authorUrl.includes(userEmail);
        const profileUrlMatch = profileData?.id && authorUrl.includes(`blogger.com/profile/${profileData.id}`);

        return idMatch || nameMatch || emailInUrl || profileUrlMatch;
      });

      console.log('🔍 Filtered user comments:', userComments.length);

      console.log('🔍 Total user comments found:', userComments.length);

      // Debug: Log post data for each comment
      userComments.forEach((comment: any, index: number) => {
        console.log(`🔍 Comment ${index + 1} post data:`, {
          commentId: comment.id,
          postId: comment.post?.id,
          postTitle: comment.post?.title,
          postUrl: comment.post?.url,
          fullPost: comment.post
        });
      });

      // Load cached posts data
      console.log('🔍 Loading cached posts data...');
      const cachedPosts = await getHistoryData('posts', 'cache') || {};
      console.log('🔍 Cached posts loaded:', Object.keys(cachedPosts).length, 'posts');

      // Process comments: add avatar and enrich with cached post data
      const processedComments = await Promise.all(userComments.map(async (comment: any) => {
        const postId = comment.post?.id;
        let postInfo = comment.post;

        // Try to get post info from cache
        if (postId && cachedPosts[postId]) {
          console.log(`🔍 Found cached post for ${postId}:`, cachedPosts[postId].title);
          postInfo = {
            id: postId,
            title: cachedPosts[postId].title,
            url: cachedPosts[postId].url,
            ...cachedPosts[postId]
          };
        } else if (postId) {
          console.log(`🔍 Post ${postId} not in cache, using API data or fetching...`);

          // If no cached data and no API data, try to fetch individual post
          if (!comment.post?.title || comment.post?.title === 'Unknown Post') {
            try {
              console.log(`🔍 Fetching individual post ${postId}...`);
              const postResponse = await fetch(
                `https://www.googleapis.com/blogger/v3/blogs/${blogConfig.blogId}/posts/${postId}?key=${blogConfig.apiKey}`
              );

              if (postResponse.ok) {
                const postData = await postResponse.json();
                console.log(`🔍 Fetched post data for ${postId}:`, postData.title);
                postInfo = {
                  id: postData.id,
                  title: postData.title,
                  url: postData.url
                };
              }
            } catch (fetchError) {
              console.error(`Error fetching post ${postId}:`, fetchError);
            }
          }
        }

        return {
          ...comment,
          post: postInfo,
          author: {
            ...comment.author,
            image: {
              url: user.picture || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name || 'User')}&background=00d4ff&color=fff&size=40`
            }
          }
        };
      }));

      // Group comments by post and organize replies
      const groupedComments = processedComments.reduce((groups: any, comment: any) => {
        const postId = comment.post?.id || 'unknown';
        if (!groups[postId]) {
          groups[postId] = {
            postTitle: comment.post?.title || 'Unknown Post',
            postUrl: comment.post?.url || '#',
            comments: [],
            replies: []
          };
        }

        // Check if this is a reply (has inReplyTo)
        if (comment.inReplyTo) {
          groups[postId].replies.push(comment);
        } else {
          groups[postId].comments.push(comment);
        }

        return groups;
      }, {});

      // Convert to structured format for clean display
      const structuredComments: any[] = [];
      Object.entries(groupedComments).forEach(([postId, group]: [string, any]) => {
        const allComments = [...group.comments, ...group.replies];

        if (allComments.length > 0) {
          structuredComments.push({
            type: 'post-group',
            id: `group-${postId}`,
            postTitle: group.postTitle,
            postUrl: group.postUrl,
            commentCount: allComments.length,
            comments: allComments
              .sort((a: any, b: any) => new Date(a.published).getTime() - new Date(b.published).getTime()) // Chronological order
              .map((comment: any) => ({
                ...comment,
                type: 'comment',
                isReply: !!comment.inReplyTo
              }))
          });
        }
      });

      console.log('🔍 Final structured comments:', structuredComments.length);
      console.log('🔍 Structured comments data:', structuredComments);

      setRecentComments(structuredComments.slice(0, 10)); // Latest 10 post groups

    } catch (error) {
      console.error('Error fetching comments from API:', error);
      setRecentComments([]);
    } finally {
      setLoadingComments(false);
    }
  };

  // Fetch favorite posts (from favorites store)
  const fetchFavoritePosts = async () => {
    console.log('🔍 fetchFavoritePosts called');
    console.log('📊 Favorites from store:', favorites);
    console.log('📊 Favorites length:', favorites?.length || 0);

    setLoadingFavorites(true);
    try {
      if (!favorites || favorites.length === 0) {
        console.log('❌ No favorites found, setting empty array');
        setFavoritesPosts([]);
        return;
      }

      // Convert favorites to post format for display
      const favoritesData = favorites.map((favorite: any) => {
        console.log('🔄 Processing favorite:', favorite);
        return {
          id: favorite.id || favorite.postId,
          title: favorite.title || 'Untitled Post',
          url: favorite.url || '#',
          thumbnail: favorite.thumbnail || '',
          publishedDate: favorite.favoriteAt || favorite.followAt || new Date().toISOString(),
          labels: favorite.labels || []
        };
      });

      console.log('✅ Processed favorites data:', favoritesData);
      setFavoritesPosts(favoritesData.slice(0, 6)); // Latest 6 favorites
    } catch (error) {
      console.error('Error fetching favorites:', error);
      setFavoritesPosts([]);
    } finally {
      setLoadingFavorites(false);
    }
  };

  // Fetch bookmarked posts
  const fetchBookmarkedPosts = async () => {
    setLoadingBookmarks(true);
    try {
      if (!bookmarks || bookmarks.length === 0) {
        setBookmarkedPosts([]);
        return;
      }

      // Convert bookmarks to post format for display
      const bookmarksData = bookmarks.map((bookmark: any) => ({
        id: bookmark.id || bookmark.postId,
        title: bookmark.title || 'Untitled Post',
        url: bookmark.url || '#',
        thumbnail: bookmark.thumbnail || '',
        publishedDate: bookmark.timestamp || bookmark.bookmarkedAt || new Date().toISOString(),
        labels: bookmark.labels || [],
        excerpt: bookmark.excerpt || ''
      }));

      setBookmarkedPosts(bookmarksData.slice(0, 8)); // Latest 8 bookmarks
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      setBookmarkedPosts([]);
    } finally {
      setLoadingBookmarks(false);
    }
  };

  // Initialize user data
  useEffect(() => {
    const init = async () => {
      // Prevent multiple initializations
      if (hasInitialized) {
        console.log('🔄 Already initialized, skipping...');
        return;
      }

      try {
        setIsLoading(true);
        setHasInitialized(true);

        console.log('🚀 Starting initialization...', { isAuthenticated, userId });

        // Skip initializeUser - use data from store directly to avoid userinfo API calls
        // User data should already be available from useAuth hook initialization

        // If authenticated, initialize bookmark/follow store
        if (isAuthenticated && userId) {
          console.log('🚀 Initializing store for user:', userId);
          await initializeStore(userId);
          console.log('✅ Store initialized');
          console.log('📊 Store state after init - favorites:', favorites?.length || 0, 'bookmarks:', bookmarks?.length || 0);

          // Fetch user-specific data (DISABLE comments to prevent spam)
          await Promise.all([
            // fetchRecentComments(), // DISABLED - causing API spam
            fetchFavoritePosts(),
            fetchBookmarkedPosts()
          ]);
        }
      } catch (error) {
        console.error('Error initializing user page:', error);
        setHasInitialized(false); // Reset on error
        toast({
          title: 'Error',
          description: 'Failed to load user data',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Only run if not already initialized
    if (!hasInitialized) {
      init();
    }
  }, [isAuthenticated, userId, hasInitialized]); // Add hasInitialized to deps

  // Re-fetch data when favorites/bookmarks change (DISABLED to prevent infinite loops)
  // useEffect(() => {
  //   console.log('🔄 Favorites changed:', favorites?.length || 0, 'items');
  //   if (isAuthenticated && !isLoading && hasInitialized) {
  //     console.log('🔄 Re-fetching favorites due to store change');
  //     fetchFavoritePosts();
  //   }
  // }, [favorites, isAuthenticated, isLoading, hasInitialized]);

  // useEffect(() => {
  //   if (isAuthenticated && !isLoading && hasInitialized) {
  //     fetchBookmarkedPosts();
  //   }
  // }, [bookmarks, isAuthenticated, isLoading, hasInitialized]);

  // Handle delete comment
  const handleDeleteComment = async (comment: any) => {
    setCommentToDelete(comment);
    onDeleteOpen();
  };

  const confirmDeleteComment = async () => {
    // Debug: Check all possible token sources
    console.log('🔍 Debug token sources:', {
      accessToken,
      userStoreToken: useUserStore.getState().accessToken,
      hasComment: !!commentToDelete,
      user: user?.email
    });

    // Try to get token from user store if not available
    const token = accessToken || useUserStore.getState().accessToken;

    if (!commentToDelete || !token) {
      console.log('❌ Missing comment or access token:', {
        hasComment: !!commentToDelete,
        hasToken: !!token,
        accessToken,
        storeToken: useUserStore.getState().accessToken
      });

      toast({
        title: 'Delete Failed',
        description: 'Authentication required. Please refresh and try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    console.log('🗑️ Starting comment deletion:', {
      commentId: commentToDelete.id,
      postData: commentToDelete.post,
      accessToken: accessToken ? 'present' : 'missing'
    });

    setDeletingCommentId(commentToDelete.id);
    try {
      // Try multiple ways to get post ID
      let postId = commentToDelete.post?.id;

      if (!postId && commentToDelete.post?.url) {
        // Extract from URL
        const urlParts = commentToDelete.post.url.split('/');
        postId = urlParts[urlParts.length - 1]?.replace('.html', '');
      }

      if (!postId) {
        // Try to get from comment ID structure
        const commentIdParts = commentToDelete.id.split('.');
        if (commentIdParts.length > 1) {
          postId = commentIdParts[1];
        }
      }

      console.log('🔍 Extracted post ID:', postId);

      if (!postId) {
        throw new Error('Could not extract post ID from comment');
      }

      console.log('🔄 Calling delete API...');
      await commentService.deleteComment(commentToDelete.id, postId, token);
      console.log('✅ Comment deleted successfully via API');

      // Remove comment from local state (handle grouped structure)
      setRecentComments(prev => {
        return prev.map(postGroup => ({
          ...postGroup,
          comments: postGroup.comments.filter((c: any) => c.id !== commentToDelete.id),
          commentCount: postGroup.comments.filter((c: any) => c.id !== commentToDelete.id).length
        })).filter(postGroup => postGroup.comments.length > 0); // Remove empty groups
      });

      console.log('✅ Comment removed from local state');

      toast({
        title: 'Comment Deleted',
        description: 'Your comment has been deleted successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      onDeleteClose();
      setCommentToDelete(null);
    } catch (error: any) {
      console.error('❌ Error deleting comment:', error);
      toast({
        title: 'Delete Failed',
        description: error.message || 'Failed to delete comment',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setDeletingCommentId(null);
    }
  };

  // Handle login
  const handleLogin = async () => {
    setIsProcessing(true);
    try {
      // Login will be handled by GoogleStyleLoginButton
      console.log('Login initiated from User page');
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: 'Login Error',
        description: 'Failed to initiate login process',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Loading state - show loading while determining auth state
  if (isAuthLoading) {
    return (
      <Box bg={bgColor} minH="100vh" color={textColor}>
        <Container maxW="1200px" py={8}>
          <Center h="50vh">
            <VStack spacing={4}>
              <Spinner size="xl" color={accentColor} />
              <Text color={mutedColor}>Loading user data...</Text>
            </VStack>
          </Center>
        </Container>
      </Box>
    );
  }

  // Not authenticated state
  if (!isAuthenticated) {
    return (
      <Box bg={bgColor} minH="100vh" color={textColor}>
        <Container maxW="1200px" py={8}>
          <Center h="50vh">
            <VStack spacing={8} textAlign="center" maxW="400px">
              <Icon as={FaUser} boxSize={16} color={mutedColor} />
              <VStack spacing={3}>
                <Text fontSize="2xl" fontWeight="bold">
                  welcome to user page
                </Text>
                <Text color={mutedColor} fontSize="sm">
                  sign in with your google account to access your profile, bookmarks, and personalized features
                </Text>
              </VStack>

              {/* Google Style Login Button */}
              <VStack spacing={4} w="100%">
                <GoogleStyleLoginButton
                  variant="full"
                  onSuccess={() => {
                    // Show single toast from UserPage
                    console.log('Login success callback from UserPage');
                    toast({
                      title: 'Đăng nhập thành công',
                      description: 'Chào mừng bạn quay trở lại!',
                      status: 'success',
                      duration: 3000,
                      isClosable: true,
                    });

                    // No need to reload - UserPage should auto-update via useUserStore subscription
                  }}
                  onError={() => {
                    toast({
                      title: 'Login Failed',
                      description: 'Unable to sign in. Please try again.',
                      status: 'error',
                      duration: 4000,
                      isClosable: true,
                    });
                  }}
                />

                {/* Alternative login button */}
                <Button
                  leftIcon={<FaGoogle />}
                  variant="outline"
                  size="md"
                  w="100%"
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{
                    bg: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                    borderColor: accentColor
                  }}
                  isLoading={isProcessing}
                  onClick={handleLogin}
                >
                  alternative login method
                </Button>
              </VStack>

              <Text fontSize="xs" color={mutedColor} textAlign="center" lineHeight="1.4">
                by signing in, you agree to our terms of service and privacy policy.
                your data is securely stored and never shared with third parties.
              </Text>
            </VStack>
          </Center>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} minH="100vh" color={textColor}>
      <Container maxW="1200px" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <Text fontSize="3xl" fontWeight="bold" mb={2}>
              my content
            </Text>
            <Text color={mutedColor}>
              view your comments, favorites, and bookmarks
            </Text>
          </Box>

          {/* Content Tabs */}
          <Tabs variant="soft-rounded" colorScheme="blue" defaultIndex={0}>
            <TabList mb={6} justifyContent="center" flexWrap="wrap">
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaComment} mr={2} />
                recent comments
              </Tab>
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaHeart} mr={2} />
                favorites
              </Tab>
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaBookmark} mr={2} />
                bookmarks
              </Tab>
            </TabList>

            <TabPanels>
              {/* Recent Comments Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">recent comments</Text>
                    <Badge colorScheme="blue" variant="subtle">{recentComments.length}</Badge>
                  </HStack>

                  {loadingComments ? (
                    <VStack spacing={3}>
                      {[...Array(3)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={3}>
                              <Skeleton height="40px" width="40px" borderRadius="full" />
                              <VStack align="start" flex={1} spacing={2}>
                                <SkeletonText noOfLines={1} width="60%" />
                                <SkeletonText noOfLines={2} width="100%" />
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  ) : (console.log('🎯 Rendering comments:', recentComments), recentComments.length === 0) ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaComment} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no recent comments found
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              start commenting on posts to see them here
                            </Text>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <VStack spacing={6}>
                      {recentComments.map((postGroup) => (
                        <Card key={postGroup.id} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%" overflow="hidden">
                          {/* Post Header */}
                          <Box bg={isDark ? '#1a1a1a' : '#f8f9fa'} px={6} py={4} borderBottom="1px solid" borderColor={borderColor}>
                            <HStack justify="space-between" align="center">
                              <HStack spacing={3}>
                                <Icon as={FaComment} color={accentColor} boxSize="20px" />
                                <VStack align="start" spacing={0}>
                                  <Text fontSize="md" fontWeight="bold" color={textColor} noOfLines={1}>
                                    {postGroup.postTitle}
                                  </Text>
                                  <Text fontSize="xs" color={mutedColor}>
                                    {postGroup.commentCount} comment{postGroup.commentCount !== 1 ? 's' : ''}
                                  </Text>
                                </VStack>
                              </HStack>
                              <Button
                                as={Link}
                                to={postGroup.postUrl}
                                size="sm"
                                variant="outline"
                                leftIcon={<FaEye />}
                                color={accentColor}
                                borderColor={accentColor}
                                _hover={{ bg: accentColor, color: 'white' }}
                              >
                                view post
                              </Button>
                            </HStack>
                          </Box>

                          {/* Comments List */}
                          <CardBody p={0}>
                            <VStack spacing={0} align="stretch">
                              {postGroup.comments.map((comment: any, commentIndex: number) => (
                                <Box
                                  key={comment.id}
                                  px={6}
                                  py={4}
                                  borderBottom={commentIndex < postGroup.comments.length - 1 ? "1px solid" : "none"}
                                  borderColor={borderColor}
                                  pl={comment.isReply ? 12 : 6} // Indent replies
                                  bg={comment.isReply ? (isDark ? '#0a0a0a' : '#fafafa') : 'transparent'}
                                >
                                  <HStack justify="space-between" align="start" spacing={4}>
                                    <VStack align="start" spacing={3} flex={1}>
                                      {/* Comment metadata */}
                                      <HStack spacing={2}>
                                        {comment.isReply && (
                                          <Icon as={FaComment} boxSize="12px" color={mutedColor} transform="scaleX(-1)" />
                                        )}
                                        <Icon as={FaClock} boxSize="12px" color={mutedColor} />
                                        <Text fontSize="xs" color={mutedColor}>
                                          {new Date(comment.published).toLocaleDateString()} at {new Date(comment.published).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                        </Text>
                                        {comment.isReply && (
                                          <Text fontSize="xs" color={accentColor} fontWeight="medium">
                                            • reply
                                          </Text>
                                        )}
                                      </HStack>

                                      {/* Comment content */}
                                      <Text fontSize="sm" color={textColor} lineHeight="1.5">
                                        {comment.content?.replace(/<[^>]*>/g, '') || 'No content'}
                                      </Text>
                                    </VStack>

                                    {/* Delete button */}
                                    <IconButton
                                      aria-label="Delete comment"
                                      icon={<FaTrash />}
                                      size="sm"
                                      variant="ghost"
                                      colorScheme="red"
                                      onClick={() => handleDeleteComment(comment)}
                                      isLoading={deletingCommentId === comment.id}
                                      _hover={{ bg: 'red.100' }}
                                      opacity={0.7}
                                    />
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  )}
                </VStack>
              </TabPanel>

              {/* Favorites Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">favorite posts</Text>
                    <Badge colorScheme="pink" variant="subtle">{favoritesPosts.length}</Badge>
                  </HStack>

                  {loadingFavorites ? (
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                      {[...Array(6)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor}>
                          <Skeleton height="120px" />
                          <CardBody>
                            <VStack align="start" spacing={2}>
                              <SkeletonText noOfLines={2} width="100%" />
                              <SkeletonText noOfLines={1} width="60%" />
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  ) : (console.log('🎯 Rendering favorites:', favoritesPosts), favoritesPosts.length === 0) ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaHeart} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no favorite posts yet
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              follow posts you love to see them here
                            </Text>
                            <Button
                              as={Link}
                              to="/"
                              size="sm"
                              variant="outline"
                              colorScheme="pink"
                            >
                              explore posts
                            </Button>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                      {favoritesPosts.map((post, index) => (
                        <Card key={index} bg={cardBg} border="1px solid" borderColor={borderColor} overflow="hidden">
                          {post.thumbnail && (
                            <Image
                              src={post.thumbnail}
                              alt={post.title}
                              height="120px"
                              width="100%"
                              objectFit="cover"
                              fallback={
                                <Box height="120px" bg={isDark ? '#2a2a2a' : '#f0f0f0'} display="flex" alignItems="center" justifyContent="center">
                                  <Icon as={FaHeart} boxSize={8} color={mutedColor} />
                                </Box>
                              }
                            />
                          )}
                          <CardBody>
                            <VStack align="start" spacing={3}>
                              <Text fontSize="sm" fontWeight="semibold" noOfLines={2} lineHeight="1.3">
                                {post.title}
                              </Text>
                              <HStack justify="space-between" w="100%">
                                <HStack spacing={1}>
                                  <Icon as={FaClock} boxSize="12px" color={mutedColor} />
                                  <Text fontSize="xs" color={mutedColor}>
                                    {new Date(post.publishedDate).toLocaleDateString()}
                                  </Text>
                                </HStack>
                                <Button
                                  as={Link}
                                  to={post.url}
                                  size="xs"
                                  variant="ghost"
                                  color={accentColor}
                                  leftIcon={<FaEye />}
                                >
                                  read
                                </Button>
                              </HStack>
                              {post.labels && post.labels.length > 0 && (
                                <Wrap spacing={1}>
                                  {post.labels.slice(0, 3).map((label: string, labelIndex: number) => (
                                    <WrapItem key={labelIndex}>
                                      <Tag size="sm" variant="subtle" colorScheme="pink">
                                        <TagLeftIcon as={FaTags} />
                                        <TagLabel fontSize="xs">{label}</TagLabel>
                                      </Tag>
                                    </WrapItem>
                                  ))}
                                </Wrap>
                              )}
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  )}
                </VStack>
              </TabPanel>

              {/* Bookmarks Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">bookmarked posts</Text>
                    <Badge colorScheme="green" variant="subtle">{bookmarkedPosts.length}</Badge>
                  </HStack>

                  {loadingBookmarks ? (
                    <VStack spacing={3}>
                      {[...Array(4)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={4}>
                              <Skeleton height="80px" width="80px" borderRadius="md" />
                              <VStack align="start" flex={1} spacing={2}>
                                <SkeletonText noOfLines={2} width="100%" />
                                <SkeletonText noOfLines={1} width="70%" />
                                <SkeletonText noOfLines={1} width="50%" />
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  ) : bookmarkedPosts.length === 0 ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaBookmark} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no bookmarked posts yet
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              bookmark posts to read later and see them here
                            </Text>
                            <Button
                              as={Link}
                              to="/"
                              size="sm"
                              variant="outline"
                              colorScheme="green"
                            >
                              explore posts
                            </Button>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <VStack spacing={3}>
                      {bookmarkedPosts.map((post, index) => (
                        <Card key={index} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={4} align="start">
                              {post.thumbnail ? (
                                <Image
                                  src={post.thumbnail}
                                  alt={post.title}
                                  width="80px"
                                  height="80px"
                                  objectFit="cover"
                                  borderRadius="md"
                                  fallback={
                                    <Box
                                      width="80px"
                                      height="80px"
                                      bg={isDark ? '#2a2a2a' : '#f0f0f0'}
                                      borderRadius="md"
                                      display="flex"
                                      alignItems="center"
                                      justifyContent="center"
                                    >
                                      <Icon as={FaBookmark} boxSize={6} color={mutedColor} />
                                    </Box>
                                  }
                                />
                              ) : (
                                <Box
                                  width="80px"
                                  height="80px"
                                  bg={isDark ? '#2a2a2a' : '#f0f0f0'}
                                  borderRadius="md"
                                  display="flex"
                                  alignItems="center"
                                  justifyContent="center"
                                >
                                  <Icon as={FaBookmark} boxSize={6} color={mutedColor} />
                                </Box>
                              )}

                              <VStack align="start" flex={1} spacing={2}>
                                <Text fontSize="md" fontWeight="semibold" noOfLines={2} lineHeight="1.4">
                                  {post.title}
                                </Text>
                                {post.excerpt && (
                                  <Text fontSize="sm" color={mutedColor} noOfLines={2}>
                                    {post.excerpt}
                                  </Text>
                                )}
                                <HStack justify="space-between" w="100%">
                                  <HStack spacing={2}>
                                    <Icon as={FaClock} boxSize="12px" color={mutedColor} />
                                    <Text fontSize="xs" color={mutedColor}>
                                      {new Date(post.publishedDate).toLocaleDateString()}
                                    </Text>
                                  </HStack>
                                  <Button
                                    as={Link}
                                    to={post.url}
                                    size="sm"
                                    variant="solid"
                                    colorScheme="green"
                                    leftIcon={<FaEye />}
                                  >
                                    read now
                                  </Button>
                                </HStack>
                                {post.labels && post.labels.length > 0 && (
                                  <Wrap spacing={1} mt={1}>
                                    {post.labels.slice(0, 4).map((label: string, labelIndex: number) => (
                                      <WrapItem key={labelIndex}>
                                        <Tag size="sm" variant="outline" colorScheme="green">
                                          <TagLabel fontSize="xs">{label}</TagLabel>
                                        </Tag>
                                      </WrapItem>
                                    ))}
                                  </Wrap>
                                )}
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Container>

      {/* Delete Comment Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>delete comment</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Alert status="warning" mb={4}>
              <AlertIcon />
              <Text>are you sure you want to delete this comment? this action cannot be undone.</Text>
            </Alert>
            {commentToDelete && (
              <Box p={3} bg={isDark ? '#1a1a1a' : '#f7f7f7'} borderRadius="md">
                <Text fontSize="sm" noOfLines={3}>
                  {commentToDelete.content?.replace(/<[^>]*>/g, '') || 'No content'}
                </Text>
                <Text fontSize="xs" color={mutedColor} mt={2}>
                  posted on {new Date(commentToDelete.published).toLocaleDateString()}
                </Text>
              </Box>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onDeleteClose}>
              cancel
            </Button>
            <Button
              colorScheme="red"
              onClick={confirmDeleteComment}
              isLoading={deletingCommentId === commentToDelete?.id}
            >
              delete
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default UserPage;
