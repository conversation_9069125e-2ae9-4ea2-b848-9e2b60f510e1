import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Avatar,
  Button,
  Card,
  CardBody,
  Badge,
  useColorMode,
  useToast,
  Spinner,
  Center,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Image,
  SimpleGrid,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  TagLeftIcon,
  Skeleton,
  SkeletonText,
} from '@chakra-ui/react';
import { FaUser, FaBookmark, FaHeart, FaSignInAlt, FaGoogle, FaComment, FaClock, FaEye, FaTags } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import useUserStore from '../store/useUserStore';
import useFavoriteBookmarkStore from '../store/useFollowBookmarkStore';
import { useAuth } from '../hooks/useAuthNew';
import LoginButton from '../components/Auth/LoginButton';
import GoogleStyleLoginButton from '../components/Auth/GoogleStyleLoginButton';
import { blogConfig } from '../config';

const UserPage: React.FC = () => {
  const { colorMode } = useColorMode();
  const toast = useToast();
  const navigate = useNavigate();

  // Auth hooks
  const {
    isAuthenticated,
    user,
    isLoading: authLoading,
    login,
    logout
  } = useAuth();

  // Store hooks
  const {
    userId,
    accessToken,
    initializeUser
  } = useUserStore();

  const {
    bookmarks,
    favorites,
    initialize: initializeStore
  } = useFavoriteBookmarkStore();

  // Local state
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recentComments, setRecentComments] = useState<any[]>([]);
  const [favoritesPosts, setFavoritesPosts] = useState<any[]>([]);
  const [bookmarkedPosts, setBookmarkedPosts] = useState<any[]>([]);
  const [loadingComments, setLoadingComments] = useState(false);
  const [loadingFavorites, setLoadingFavorites] = useState(false);
  const [loadingBookmarks, setLoadingBookmarks] = useState(false);

  // Theme colors
  const isDark = colorMode === 'dark';
  const bgColor = isDark ? '#000000' : '#ffffff';
  const cardBg = isDark ? '#131313' : '#ffffff';
  const textColor = isDark ? '#ffffff' : '#000000';
  const mutedColor = isDark ? '#cccccc' : '#666666';
  const borderColor = isDark ? '#333333' : '#e5e5e5';
  const accentColor = '#00d4ff';

  // Fetch recent comments from Blogger API
  const fetchRecentComments = async () => {
    if (!user?.email || !accessToken) return;

    setLoadingComments(true);
    try {
      // Fetch comments from Blogger API with access token
      const response = await fetch(
        `https://www.googleapis.com/blogger/v3/blogs/${blogConfig.blogId}/comments?access_token=${accessToken}&maxResults=10&orderBy=published&status=live`
      );

      if (response.ok) {
        const data = await response.json();
        console.log('🔍 Comments API response:', data);
        console.log('🔍 Total comments:', data.items?.length || 0);
        console.log('🔍 Current user info:', { name: user.name, email: user.email });

        // Debug: Log all comment authors
        data.items?.forEach((comment: any, index: number) => {
          console.log(`🔍 Comment ${index + 1}:`, {
            author: comment.author?.displayName,
            email: comment.author?.url,
            content: comment.content?.substring(0, 50) + '...'
          });
        });

        // Filter comments by current user email
        const userComments = data.items?.filter((comment: any) =>
          comment.author?.displayName?.toLowerCase().includes(user.name?.toLowerCase()) ||
          comment.author?.url?.includes(user.email)
        ) || [];

        console.log('🔍 Filtered user comments:', userComments.length);
        console.log('🔍 User comments data:', userComments);

        setRecentComments(userComments.slice(0, 5)); // Latest 5 comments
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
    } finally {
      setLoadingComments(false);
    }
  };

  // Fetch favorite posts (from favorites store)
  const fetchFavoritePosts = async () => {
    console.log('🔍 fetchFavoritePosts called');
    console.log('📊 Favorites from store:', favorites);
    console.log('📊 Favorites length:', favorites?.length || 0);

    setLoadingFavorites(true);
    try {
      if (!favorites || favorites.length === 0) {
        console.log('❌ No favorites found, setting empty array');
        setFavoritesPosts([]);
        return;
      }

      // Convert favorites to post format for display
      const favoritesData = favorites.map((favorite: any) => {
        console.log('🔄 Processing favorite:', favorite);
        return {
          id: favorite.id || favorite.postId,
          title: favorite.title || 'Untitled Post',
          url: favorite.url || '#',
          thumbnail: favorite.thumbnail || '',
          publishedDate: favorite.favoriteAt || favorite.followAt || new Date().toISOString(),
          labels: favorite.labels || []
        };
      });

      console.log('✅ Processed favorites data:', favoritesData);
      setFavoritesPosts(favoritesData.slice(0, 6)); // Latest 6 favorites
    } catch (error) {
      console.error('Error fetching favorites:', error);
      setFavoritesPosts([]);
    } finally {
      setLoadingFavorites(false);
    }
  };

  // Fetch bookmarked posts
  const fetchBookmarkedPosts = async () => {
    setLoadingBookmarks(true);
    try {
      if (!bookmarks || bookmarks.length === 0) {
        setBookmarkedPosts([]);
        return;
      }

      // Convert bookmarks to post format for display
      const bookmarksData = bookmarks.map((bookmark: any) => ({
        id: bookmark.id || bookmark.postId,
        title: bookmark.title || 'Untitled Post',
        url: bookmark.url || '#',
        thumbnail: bookmark.thumbnail || '',
        publishedDate: bookmark.timestamp || bookmark.bookmarkedAt || new Date().toISOString(),
        labels: bookmark.labels || [],
        excerpt: bookmark.excerpt || ''
      }));

      setBookmarkedPosts(bookmarksData.slice(0, 8)); // Latest 8 bookmarks
    } catch (error) {
      console.error('Error fetching bookmarks:', error);
      setBookmarkedPosts([]);
    } finally {
      setLoadingBookmarks(false);
    }
  };

  // Initialize user data
  useEffect(() => {
    const init = async () => {
      try {
        setIsLoading(true);
        
        // Initialize user store
        await initializeUser();
        
        // If authenticated, initialize bookmark/follow store
        if (isAuthenticated && userId) {
          console.log('🚀 Initializing store for user:', userId);
          await initializeStore(userId);
          console.log('✅ Store initialized');
          console.log('📊 Store state after init - favorites:', favorites?.length || 0, 'bookmarks:', bookmarks?.length || 0);

          // Fetch user-specific data
          await Promise.all([
            fetchRecentComments(),
            fetchFavoritePosts(),
            fetchBookmarkedPosts()
          ]);
        }
      } catch (error) {
        console.error('Error initializing user page:', error);
        toast({
          title: 'Error',
          description: 'Failed to load user data',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    init();
  }, [initializeUser, isAuthenticated, userId, accessToken, initializeStore, toast, user]);

  // Re-fetch data when favorites/bookmarks change
  useEffect(() => {
    console.log('🔄 Favorites changed:', favorites?.length || 0, 'items');
    if (isAuthenticated && !isLoading) {
      console.log('🔄 Re-fetching favorites due to store change');
      fetchFavoritePosts();
    }
  }, [favorites, isAuthenticated, isLoading]);

  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      fetchBookmarkedPosts();
    }
  }, [bookmarks, isAuthenticated, isLoading]);

  // Handle login
  const handleLogin = async () => {
    setIsProcessing(true);
    try {
      // Login will be handled by GoogleStyleLoginButton
      console.log('Login initiated from User page');
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: 'Login Error',
        description: 'Failed to initiate login process',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Loading state
  if (isLoading || authLoading) {
    return (
      <Box bg={bgColor} minH="100vh" color={textColor}>
        <Container maxW="1200px" py={8}>
          <Center h="50vh">
            <VStack spacing={4}>
              <Spinner size="xl" color={accentColor} />
              <Text color={mutedColor}>Loading user data...</Text>
            </VStack>
          </Center>
        </Container>
      </Box>
    );
  }

  // Not authenticated state
  if (!isAuthenticated) {
    return (
      <Box bg={bgColor} minH="100vh" color={textColor}>
        <Container maxW="1200px" py={8}>
          <Center h="50vh">
            <VStack spacing={8} textAlign="center" maxW="400px">
              <Icon as={FaUser} boxSize={16} color={mutedColor} />
              <VStack spacing={3}>
                <Text fontSize="2xl" fontWeight="bold">
                  welcome to user page
                </Text>
                <Text color={mutedColor} fontSize="sm">
                  sign in with your google account to access your profile, bookmarks, and personalized features
                </Text>
              </VStack>

              {/* Google Style Login Button */}
              <VStack spacing={4} w="100%">
                <GoogleStyleLoginButton
                  variant="full"
                  onSuccess={() => {
                    toast({
                      title: 'Login Successful',
                      description: 'Welcome back! Your profile is now available.',
                      status: 'success',
                      duration: 4000,
                      isClosable: true,
                    });
                  }}
                  onError={() => {
                    toast({
                      title: 'Login Failed',
                      description: 'Unable to sign in. Please try again.',
                      status: 'error',
                      duration: 4000,
                      isClosable: true,
                    });
                  }}
                />

                {/* Alternative login button */}
                <Button
                  leftIcon={<FaGoogle />}
                  variant="outline"
                  size="md"
                  w="100%"
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{
                    bg: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                    borderColor: accentColor
                  }}
                  isLoading={isProcessing}
                  onClick={handleLogin}
                >
                  alternative login method
                </Button>
              </VStack>

              <Text fontSize="xs" color={mutedColor} textAlign="center" lineHeight="1.4">
                by signing in, you agree to our terms of service and privacy policy.
                your data is securely stored and never shared with third parties.
              </Text>
            </VStack>
          </Center>
        </Container>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} minH="100vh" color={textColor}>
      <Container maxW="1200px" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box textAlign="center">
            <Text fontSize="3xl" fontWeight="bold" mb={2}>
              my content
            </Text>
            <Text color={mutedColor}>
              view your comments, favorites, and bookmarks
            </Text>
          </Box>

          {/* Content Tabs */}
          <Tabs variant="soft-rounded" colorScheme="blue" defaultIndex={0}>
            <TabList mb={6} justifyContent="center" flexWrap="wrap">
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaComment} mr={2} />
                recent comments
              </Tab>
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaHeart} mr={2} />
                favorites
              </Tab>
              <Tab fontSize="sm" _selected={{ color: 'white', bg: accentColor }}>
                <Icon as={FaBookmark} mr={2} />
                bookmarks
              </Tab>
            </TabList>

            <TabPanels>
              {/* Recent Comments Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">recent comments</Text>
                    <Badge colorScheme="blue" variant="subtle">{recentComments.length}</Badge>
                  </HStack>

                  {loadingComments ? (
                    <VStack spacing={3}>
                      {[...Array(3)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={3}>
                              <Skeleton height="40px" width="40px" borderRadius="full" />
                              <VStack align="start" flex={1} spacing={2}>
                                <SkeletonText noOfLines={1} width="60%" />
                                <SkeletonText noOfLines={2} width="100%" />
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  ) : recentComments.length === 0 ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaComment} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no recent comments found
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              start commenting on posts to see them here
                            </Text>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <VStack spacing={3}>
                      {recentComments.map((comment, index) => (
                        <Card key={index} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <VStack align="start" spacing={3}>
                              <HStack justify="space-between" w="100%">
                                <HStack spacing={3}>
                                  <Avatar size="sm" src={comment.author?.image?.url} name={comment.author?.displayName} />
                                  <VStack align="start" spacing={0}>
                                    <Text fontSize="sm" fontWeight="semibold">{comment.author?.displayName}</Text>
                                    <HStack spacing={2}>
                                      <Icon as={FaClock} size="xs" color={mutedColor} />
                                      <Text fontSize="xs" color={mutedColor}>
                                        {new Date(comment.published).toLocaleDateString()}
                                      </Text>
                                    </HStack>
                                  </VStack>
                                </HStack>
                                <Button
                                  as={Link}
                                  to={comment.post?.url || '#'}
                                  size="xs"
                                  variant="ghost"
                                  leftIcon={<FaEye />}
                                  color={accentColor}
                                >
                                  view post
                                </Button>
                              </HStack>
                              <Text fontSize="sm" color={textColor} noOfLines={3}>
                                {comment.content?.replace(/<[^>]*>/g, '') || 'No content'}
                              </Text>
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  )}
                </VStack>
              </TabPanel>

              {/* Favorites Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">favorite posts</Text>
                    <Badge colorScheme="pink" variant="subtle">{favoritesPosts.length}</Badge>
                  </HStack>

                  {loadingFavorites ? (
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                      {[...Array(6)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor}>
                          <Skeleton height="120px" />
                          <CardBody>
                            <VStack align="start" spacing={2}>
                              <SkeletonText noOfLines={2} width="100%" />
                              <SkeletonText noOfLines={1} width="60%" />
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  ) : (console.log('🎯 Rendering favorites:', favoritesPosts), favoritesPosts.length === 0) ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaHeart} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no favorite posts yet
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              follow posts you love to see them here
                            </Text>
                            <Button
                              as={Link}
                              to="/"
                              size="sm"
                              variant="outline"
                              colorScheme="pink"
                            >
                              explore posts
                            </Button>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                      {favoritesPosts.map((post, index) => (
                        <Card key={index} bg={cardBg} border="1px solid" borderColor={borderColor} overflow="hidden">
                          {post.thumbnail && (
                            <Image
                              src={post.thumbnail}
                              alt={post.title}
                              height="120px"
                              width="100%"
                              objectFit="cover"
                              fallback={
                                <Box height="120px" bg={isDark ? '#2a2a2a' : '#f0f0f0'} display="flex" alignItems="center" justifyContent="center">
                                  <Icon as={FaHeart} boxSize={8} color={mutedColor} />
                                </Box>
                              }
                            />
                          )}
                          <CardBody>
                            <VStack align="start" spacing={3}>
                              <Text fontSize="sm" fontWeight="semibold" noOfLines={2} lineHeight="1.3">
                                {post.title}
                              </Text>
                              <HStack justify="space-between" w="100%">
                                <HStack spacing={1}>
                                  <Icon as={FaClock} size="xs" color={mutedColor} />
                                  <Text fontSize="xs" color={mutedColor}>
                                    {new Date(post.publishedDate).toLocaleDateString()}
                                  </Text>
                                </HStack>
                                <Button
                                  as={Link}
                                  to={post.url}
                                  size="xs"
                                  variant="ghost"
                                  color={accentColor}
                                  leftIcon={<FaEye />}
                                >
                                  read
                                </Button>
                              </HStack>
                              {post.labels && post.labels.length > 0 && (
                                <Wrap spacing={1}>
                                  {post.labels.slice(0, 3).map((label: string, labelIndex: number) => (
                                    <WrapItem key={labelIndex}>
                                      <Tag size="sm" variant="subtle" colorScheme="pink">
                                        <TagLeftIcon as={FaTags} />
                                        <TagLabel fontSize="xs">{label}</TagLabel>
                                      </Tag>
                                    </WrapItem>
                                  ))}
                                </Wrap>
                              )}
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  )}
                </VStack>
              </TabPanel>

              {/* Bookmarks Tab */}
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="lg" fontWeight="semibold">bookmarked posts</Text>
                    <Badge colorScheme="green" variant="subtle">{bookmarkedPosts.length}</Badge>
                  </HStack>

                  {loadingBookmarks ? (
                    <VStack spacing={3}>
                      {[...Array(4)].map((_, i) => (
                        <Card key={i} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={4}>
                              <Skeleton height="80px" width="80px" borderRadius="md" />
                              <VStack align="start" flex={1} spacing={2}>
                                <SkeletonText noOfLines={2} width="100%" />
                                <SkeletonText noOfLines={1} width="70%" />
                                <SkeletonText noOfLines={1} width="50%" />
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  ) : bookmarkedPosts.length === 0 ? (
                    <Card bg={cardBg} border="1px solid" borderColor={borderColor}>
                      <CardBody>
                        <Center py={8}>
                          <VStack spacing={3}>
                            <Icon as={FaBookmark} boxSize={12} color={mutedColor} />
                            <Text color={mutedColor} textAlign="center">
                              no bookmarked posts yet
                            </Text>
                            <Text fontSize="sm" color={mutedColor} textAlign="center">
                              bookmark posts to read later and see them here
                            </Text>
                            <Button
                              as={Link}
                              to="/"
                              size="sm"
                              variant="outline"
                              colorScheme="green"
                            >
                              explore posts
                            </Button>
                          </VStack>
                        </Center>
                      </CardBody>
                    </Card>
                  ) : (
                    <VStack spacing={3}>
                      {bookmarkedPosts.map((post, index) => (
                        <Card key={index} bg={cardBg} border="1px solid" borderColor={borderColor} w="100%">
                          <CardBody>
                            <HStack spacing={4} align="start">
                              {post.thumbnail ? (
                                <Image
                                  src={post.thumbnail}
                                  alt={post.title}
                                  width="80px"
                                  height="80px"
                                  objectFit="cover"
                                  borderRadius="md"
                                  fallback={
                                    <Box
                                      width="80px"
                                      height="80px"
                                      bg={isDark ? '#2a2a2a' : '#f0f0f0'}
                                      borderRadius="md"
                                      display="flex"
                                      alignItems="center"
                                      justifyContent="center"
                                    >
                                      <Icon as={FaBookmark} boxSize={6} color={mutedColor} />
                                    </Box>
                                  }
                                />
                              ) : (
                                <Box
                                  width="80px"
                                  height="80px"
                                  bg={isDark ? '#2a2a2a' : '#f0f0f0'}
                                  borderRadius="md"
                                  display="flex"
                                  alignItems="center"
                                  justifyContent="center"
                                >
                                  <Icon as={FaBookmark} boxSize={6} color={mutedColor} />
                                </Box>
                              )}

                              <VStack align="start" flex={1} spacing={2}>
                                <Text fontSize="md" fontWeight="semibold" noOfLines={2} lineHeight="1.4">
                                  {post.title}
                                </Text>
                                {post.excerpt && (
                                  <Text fontSize="sm" color={mutedColor} noOfLines={2}>
                                    {post.excerpt}
                                  </Text>
                                )}
                                <HStack justify="space-between" w="100%">
                                  <HStack spacing={2}>
                                    <Icon as={FaClock} size="xs" color={mutedColor} />
                                    <Text fontSize="xs" color={mutedColor}>
                                      {new Date(post.publishedDate).toLocaleDateString()}
                                    </Text>
                                  </HStack>
                                  <Button
                                    as={Link}
                                    to={post.url}
                                    size="sm"
                                    variant="solid"
                                    colorScheme="green"
                                    leftIcon={<FaEye />}
                                  >
                                    read now
                                  </Button>
                                </HStack>
                                {post.labels && post.labels.length > 0 && (
                                  <Wrap spacing={1} mt={1}>
                                    {post.labels.slice(0, 4).map((label: string, labelIndex: number) => (
                                      <WrapItem key={labelIndex}>
                                        <Tag size="sm" variant="outline" colorScheme="green">
                                          <TagLabel fontSize="xs">{label}</TagLabel>
                                        </Tag>
                                      </WrapItem>
                                    ))}
                                  </Wrap>
                                )}
                              </VStack>
                            </HStack>
                          </CardBody>
                        </Card>
                      ))}
                    </VStack>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Container>
    </Box>
  );
};

export default UserPage;
