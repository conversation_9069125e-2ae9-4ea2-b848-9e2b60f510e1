import React, { useState } from 'react';
import {
  Box,
  Button,
  VStack,
  Text,
  Alert,
  AlertIcon,
  Code,
  Divider
} from '@chakra-ui/react';
import { useGoogleLogin } from '@react-oauth/google';

const LoginDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testLogin = useGoogleLogin({
    onSuccess: async (response) => {
      console.log('Debug Login Success:', response);
      setDebugInfo(response);
      setError(null);

      // Test the token immediately
      if (response.access_token) {
        try {
          const userResponse = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
            headers: {
              Authorization: `Bearer ${response.access_token}`,
            },
          });

          if (userResponse.ok) {
            const userInfo = await userResponse.json();
            console.log('User info:', userInfo);
            setDebugInfo(prev => ({ ...prev, userInfo }));
          } else {
            const errorText = await userResponse.text();
            console.error('User info error:', errorText);
            setError(`User info error: ${userResponse.status} - ${errorText}`);
          }
        } catch (err) {
          console.error('Fetch error:', err);
          setError(`Fetch error: ${err}`);
        }
      }
    },
    onError: (error) => {
      console.error('Debug Login Error:', error);
      setError(JSON.stringify(error, null, 2));
      setDebugInfo(null);
    },
    scope: 'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email',
    flow: 'implicit',
    prompt: 'consent'
  });

  return (
    <Box p={6} maxW="600px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold">
          Google OAuth Debug
        </Text>

        <Button onClick={() => testLogin()} colorScheme="blue">
          Test Google Login
        </Button>

        <Divider />

        {error && (
          <Alert status="error">
            <AlertIcon />
            <Box>
              <Text fontWeight="bold">Error:</Text>
              <Code>{error}</Code>
            </Box>
          </Alert>
        )}

        {debugInfo && (
          <Box>
            <Text fontWeight="bold" mb={2}>Debug Info:</Text>
            <Code p={4} borderRadius="md" display="block" whiteSpace="pre-wrap">
              {JSON.stringify(debugInfo, null, 2)}
            </Code>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default LoginDebug;
