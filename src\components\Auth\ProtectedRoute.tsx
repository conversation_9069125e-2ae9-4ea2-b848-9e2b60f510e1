import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import {
  Box,
  VStack,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useColorModeValue,
  Spinner,
  Center
} from '@chakra-ui/react';
import useUserStore from '../../store/useUserStore';
import LoginButton from './LoginButton';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  showLoginPrompt?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/',
  showLoginPrompt = true
}) => {
  const { isAuthenticated, storeReady, user, initializeUser } = useUserStore();
  const [isInitializing, setIsInitializing] = useState(true);
  const location = useLocation();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  // Force initialization on mount
  useEffect(() => {
    const init = async () => {
      console.log('[ProtectedRoute] Initializing user store...');
      try {
        await initializeUser();
        console.log('[ProtectedRoute] User store initialized');

        // Add small delay to ensure state is fully synced
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error('[ProtectedRoute] Failed to initialize user store:', error);
      } finally {
        setIsInitializing(false);
      }
    };

    if (!storeReady) {
      init();
    } else {
      // Even if store is ready, add small delay for state sync
      setTimeout(() => setIsInitializing(false), 50);
    }
  }, [storeReady, initializeUser]);

  console.log('[ProtectedRoute] Auth status:', {
    isAuthenticated,
    storeReady,
    isInitializing,
    hasUser: !!user,
    userId: user?.sub || user?.id
  });

  // Show loading while store is initializing
  if (isInitializing || !storeReady) {
    return (
      <Center minH="200px">
        <VStack spacing={4}>
          <Spinner size="lg" color="blue.500" />
          <Text>Đang kiểm tra đăng nhập...</Text>
        </VStack>
      </Center>
    );
  }

  // Double check authentication with user data
  const hasValidAuth = isAuthenticated && user && (user.sub || user.id);

  console.log('[ProtectedRoute] Final auth check:', {
    isAuthenticated,
    hasUser: !!user,
    hasValidAuth,
    willShowLogin: !hasValidAuth
  });

  // If not authenticated, show login prompt or redirect
  if (!hasValidAuth) {
    if (showLoginPrompt) {
      return (
        <Box
          maxW="md"
          mx="auto"
          mt={8}
          p={6}
          bg={bgColor}
          borderWidth="1px"
          borderColor={borderColor}
          borderRadius="lg"
          shadow="md"
        >
          <VStack spacing={6} textAlign="center">
            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Cần đăng nhập!</AlertTitle>
                <AlertDescription>
                  Bạn cần đăng nhập để truy cập trang này.
                </AlertDescription>
              </Box>
            </Alert>

            <Text fontSize="lg" fontWeight="medium">
              Đăng nhập để sử dụng đầy đủ tính năng
            </Text>

            <VStack spacing={4} w="full">
              <LoginButton variant="button" size="lg" />

              <Button
                variant="outline"
                onClick={() => window.history.back()}
                size="sm"
              >
                Quay lại
              </Button>
            </VStack>
          </VStack>
        </Box>
      );
    }

    // Redirect to specified route with return URL
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location }}
        replace
      />
    );
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default ProtectedRoute;
