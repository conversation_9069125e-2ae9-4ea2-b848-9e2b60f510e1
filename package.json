{"name": "blogger-react-spa", "version": "1.0.0", "private": true, "type": "module", "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.9", "@chakra-ui/theme-utils": "^2.0.21", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@glidejs/glide": "^3.7.1", "@google-cloud/local-auth": "^3.0.1", "@mui/material": "^7.1.0", "@react-oauth/google": "^0.12.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/http-proxy-middleware": "^0.19.3", "@types/jszip": "^3.4.0", "axios": "^1.9.0", "babel-plugin-flow-react-proptypes": "^26.0.0", "connect": "^3.7.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "express": "^5.1.0", "framer-motion": "^12.16.0", "google-auth-library": "^9.15.1", "http-proxy-middleware": "^3.0.5", "jszip": "^3.10.1", "lodash": "^4.17.21", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-date-range": "^2.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-helmet-async": "^2.0.5", "react-hotkeys-hook": "^5.1.0", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-lazy-load-image-component": "^1.6.3", "react-router-dom": "^6.18.0", "react-swipeable": "^7.0.2", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.6", "react-zoom-pan-pinch": "^3.7.0", "swiper": "^11.2.8", "zustand": "^5.0.5"}, "scripts": {"dev": "cross-env NODE_OPTIONS=\"--no-deprecation\" vite", "start": "cross-env NODE_OPTIONS=\"--no-deprecation\" vite", "build": "cross-env NODE_OPTIONS=\"--no-deprecation\" vite build", "preview": "cross-env NODE_OPTIONS=\"--no-deprecation\" vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,scss}\"", "serve": "cross-env NODE_OPTIONS=\"--no-deprecation\" vite preview", "server": "cross-env NODE_OPTIONS=\"--loader ts-node/esm\" node src/server/index.ts"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/glidejs__glide": "^3.6.6", "@types/lodash": "^4.17.17", "@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^18.3.7", "@types/react-lazy-load-image-component": "^1.6.4", "@types/react-transition-group": "^4.4.12", "@types/react-virtualized": "^9.22.2", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.1.0", "cross-env": "^7.0.3", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.5.3", "rimraf": "^6.0.1", "sass": "^1.69.5", "terser": "^5.42.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^4.5.0", "vite-plugin-css-injected-by-js": "^3.5.2"}}