import React from 'react';
import {
  Box,
  Container,
  Heading,
  VStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  HStack,
  Icon
} from '@chakra-ui/react';
import { useLocation } from 'react-router-dom';
import { SIDEBAR_MENU_ITEMS } from './NavConstants';

const NavigationTest: React.FC = () => {
  const location = useLocation();

  return (
    <Container maxW="container.lg" py={8}>
      <VStack spacing={6} align="stretch">
        <Heading size="lg" textAlign="center">
          Navigation Test
        </Heading>

        <Text textAlign="center" color="cobalt.textMuted">
          Current path: {location.pathname}
        </Text>

        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
          {SIDEBAR_MENU_ITEMS.map((item) => (
            <Card key={item.path}>
              <CardBody>
                <VStack spacing={3}>
                  <HStack>
                    <Box color="cobalt.primary" fontSize="24px">
                      {item.icon}
                    </Box>
                    <Badge
                      colorScheme={location.pathname === item.path ? "blue" : "gray"}
                      variant="solid"
                    >
                      {location.pathname === item.path ? "Active" : "Inactive"}
                    </Badge>
                  </HStack>
                  <Text fontWeight="semibold">
                    {item.name}
                  </Text>
                  <Text color="cobalt.textMuted" fontSize="sm">
                    Path: {item.path}
                  </Text>
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>

        <Card p={6}>
          <VStack spacing={4}>
            <Heading size="md">
              Navigation Features
            </Heading>
            <VStack spacing={2} align="stretch">
              <Text>
                ✅ Sidebar navigation for desktop (left side)
              </Text>
              <Text>
                ✅ Bottom navigation for mobile devices
              </Text>
              <Text>
                ✅ Responsive design with breakpoints
              </Text>
              <Text>
                ✅ Active state highlighting
              </Text>
              <Text>
                ✅ Hover effects and transitions
              </Text>
              <Text>
                ✅ Cobalt.tools inspired dark theme
              </Text>
            </VStack>
          </VStack>
        </Card>
      </VStack>
    </Container>
  );
};

export default NavigationTest;
