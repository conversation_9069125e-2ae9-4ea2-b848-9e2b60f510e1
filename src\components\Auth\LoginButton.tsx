import React, { useState } from 'react';
import { IconButton, Button, useToast, Box } from '@chakra-ui/react';
import { useGoogleLogin } from '@react-oauth/google';
import { MdLogin, MdLogout } from 'react-icons/md';
import { useAuth } from '../../hooks/useAuthNew';
import GoogleStyleLoginButton from './GoogleStyleLoginButton';

// Google OAuth scopes - including Blogger API for admin features
const GOOGLE_SCOPES = [
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/drive.file',
  'https://www.googleapis.com/auth/blogger' // Full Blogger access for admin
].join(' ');

interface LoginButtonProps {
  variant?: 'icon' | 'button' | 'google-style';
  size?: 'sm' | 'md' | 'lg';
  colorScheme?: string;
  w?: string;
  googleVariant?: 'compact' | 'full' | 'floating';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const LoginButton: React.FC<LoginButtonProps> = ({
  variant = 'icon',
  size = 'sm',
  colorScheme,
  w,
  googleVariant = 'compact',
  position = 'bottom-right'
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { isAuthenticated, isLoading, login, logout } = useAuth();
  const toast = useToast();

  // Google login hook
  const googleLogin = useGoogleLogin({
    onSuccess: async (response) => {
      console.log('=== Google Login Success ===');
      console.log('Response received:', {
        hasAccessToken: !!response.access_token,
        tokenType: response.token_type,
        scope: response.scope,
        expiresIn: response.expires_in
      });

      if (!response.access_token) {
        console.error('No access token in response');
        toast({
          title: 'Lỗi đăng nhập',
          description: 'Không nhận được token từ Google',
          status: 'error',
          duration: 3000,
          isClosable: true
        });
        return;
      }

      setIsProcessing(true);
      try {
        console.log('Calling login with access token...');
        await login(response.access_token);
        console.log('Login completed successfully');
      } catch (error) {
        console.error('Login failed:', error);
        // Error is already handled in useAuth
      } finally {
        setIsProcessing(false);
      }
    },
    onError: (error) => {
      console.error('=== Google Login Error ===');
      console.error('Error details:', error);
      toast({
        title: 'Lỗi đăng nhập',
        description: 'Không thể đăng nhập bằng Google',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    },
    scope: GOOGLE_SCOPES,
    flow: 'implicit',
    prompt: 'consent'
  });

  // Handle logout
  const handleLogout = async () => {
    setIsProcessing(true);
    try {
      await logout();
    } catch (error) {
      // Error is already handled in useAuth
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle login
  const handleLogin = () => {
    googleLogin();
  };

  const isButtonLoading = isLoading || isProcessing;

  // Google Style variant
  if (variant === 'google-style') {
    return (
      <GoogleStyleLoginButton
        variant={googleVariant}
        position={position}
        onSuccess={() => {
          toast({
            title: 'Đăng nhập thành công',
            description: 'Chào mừng bạn quay trở lại!',
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
        }}
        onError={() => {
          // Error already handled in GoogleStyleLoginButton
        }}
      />
    );
  }

  if (variant === 'button') {
    return (
      <Box position="relative" w={w}>
        {/* One Tap temporarily disabled */}

        <Button
          leftIcon={isAuthenticated ? <MdLogout /> : <MdLogin />}
          onClick={isAuthenticated ? handleLogout : handleLogin}
          isLoading={isButtonLoading}
          loadingText={isAuthenticated ? 'Đang đăng xuất...' : 'Đang đăng nhập...'}
          colorScheme={colorScheme || (isAuthenticated ? 'red' : 'blue')}
          size={size}
          w={w}
        >
          {isAuthenticated ? 'Đăng xuất' : 'Đăng nhập'}
        </Button>
      </Box>
    );
  }

  return (
    <IconButton
      aria-label={isAuthenticated ? 'Đăng xuất' : 'Đăng nhập'}
      icon={isAuthenticated ? <MdLogout /> : <MdLogin />}
      onClick={isAuthenticated ? handleLogout : handleLogin}
      isLoading={isButtonLoading}
      colorScheme={colorScheme || (isAuthenticated ? 'red' : 'blue')}
      size={size}
      variant="ghost"
    />
  );
};

export default LoginButton;
