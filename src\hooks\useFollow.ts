import { useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';
import { Post } from '../types/global';
import useUserStore from '../store/useUserStore';
import useFavoriteBookmarkStore from '../store/useFollowBookmarkStore';

// Local Post interface to match PostPage
interface PostData {
  id: string;
  title: string;
  content: string;
  published: string;
  url: string;
  labels?: string[];
  data?: PostData;
}

interface UseFollowReturn {
  isFollowing: (postId: string) => boolean;
  toggleFollow: (post: PostData | Post) => Promise<boolean>;
  isLoading: boolean;
}

export const useFollow = (): UseFollowReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();
  const { userId, accessToken, isAuthenticated } = useUserStore();
  const {
    toggleFavorite: storeToggleFavorite,
    isFavorited: storeIsFavorited
  } = useFavoriteBookmarkStore();

  const isFollowing = useCallback((postId: string): boolean => {
    return storeIsFavorited(postId);
  }, [storeIsFavorited]);

  const toggleFollow = useCallback(async (post: PostData | Post): Promise<boolean> => {
    // Check authentication first
    if (!isAuthenticated || !userId) {
      toast({
        title: 'Cần đăng nhập',
        description: 'Vui lòng đăng nhập để sử dụng tính năng follow',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }

    if (!post || !post.id) {
      toast({
        title: 'Lỗi',
        description: 'Không thể follow bài viết này',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }

    setIsLoading(true);

    try {
      // Convert PostData to Post format if needed
      const postForStore: Post = {
        id: post.id,
        title: post.title,
        url: post.url,
        published: post.published,
        content: post.content,
        labels: post.labels || [],
        slug: '', // Will be generated by store
        updated: post.published // Use published as fallback
      };

      const success = await storeToggleFavorite(postForStore, userId, accessToken, toast);

      if (success !== undefined) {
        const action = success ? 'đã follow' : 'đã unfollow';
        toast({
          title: 'Thành công',
          description: `Bạn ${action} bài viết "${post.title}"`,
          status: 'success',
          duration: 2000,
          isClosable: true,
        });
        return success;
      }

      return false;
    } catch (error: any) {
      console.error('Follow error:', error);
      toast({
        title: 'Lỗi',
        description: 'Có lỗi xảy ra khi thực hiện thao tác',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, userId, accessToken, storeToggleFavorite, toast]);

  return {
    isFollowing,
    toggleFollow,
    isLoading
  };
};

export default useFollow;
