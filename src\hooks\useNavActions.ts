import { useCallback } from 'react';
import { useNavigate, NavigateFunction } from 'react-router-dom';
import { useGoogleLogin, TokenResponse } from '@react-oauth/google';
import { DRIVE_SCOPE } from '../components/GoogleDriveLogin';
import { blogConfig } from '../config';
import { handleLogin, handleLogout } from '../utils/authUtils';
import { handleError } from '../api';

import { getHistoryData } from '../utils/indexedDBUtils';
import { FOLLOW_KEY } from '../utils/userUtils';

// Interfaces
interface ToastFunction {
  (options: {
    title: string;
    description: string;
    status: 'success' | 'error' | 'warning' | 'info';
    duration: number;
    isClosable: boolean;
  }): void;
}

interface FollowedPost {
  id: string;
  updated?: string;
  published?: string;
  [key: string]: any;
}

interface UseNavActionsParams {
  setUser: (userId: string, accessToken: string) => void;
  initializeUser: () => Promise<void>;
  userId: string | null;
  accessToken: string | null;
  toast: ToastFunction;
  onClose: () => void;
  setUpdatedFollowCount: (count: number) => void;
}

interface UseNavActionsReturn {
  handleOpenSearch: () => void;
  handleHistory: () => void;
  handleProfile: () => void;
  handleViewMore: () => void;
  login: () => void;
  logout: () => void;
  checkUpdatedFollows: () => Promise<void>;
}

export const useNavActions = ({
  setUser,
  initializeUser,
  userId,
  accessToken,
  toast,
  onClose,
  setUpdatedFollowCount
}: UseNavActionsParams): UseNavActionsReturn => {
  const navigate: NavigateFunction = useNavigate();
  const handleOpenSearch = useCallback((): void => {
    navigate('/search');
  }, [navigate]);

  const handleHistory = useCallback((): void => {
    navigate('/user');
  }, [navigate]);

  const handleProfile = useCallback((): void => {
    navigate('/settings');
  }, [navigate]);

  const handleViewMore = useCallback((): void => {
    navigate('/user');
  }, [navigate]);

  const login = useGoogleLogin({
    onSuccess: (response: TokenResponse) => handleLogin({
      response: response as any,
      setUser: setUser as any,
      initializeUser: initializeUser as any,
      navigate,
      toast: toast as any,
      onClose
    }),
    onError: (error: any) => {
      console.error('Google login error:', error);
      const handledError = handleError(error);
      toast({
        title: "Lỗi đăng nhập",
        description: handledError.message || "Không thể đăng nhập bằng Google",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    },
    flow: 'implicit',
    scope: DRIVE_SCOPE,
    access_type: 'offline',
    prompt: 'consent',
    redirect_uri: blogConfig.redirectUri,
    popup: true,
    popup_width: 500,
    popup_height: 600,
    popup_position: 'center',
    popup_type: 'window',
    popup_features: 'width=500,height=600,left=0,top=0,resizable=yes,scrollbars=yes,status=yes'
  });

  const logout = useCallback((): void => {
    handleLogout({
      userId: userId || '',
      navigate,
      toast: toast as any,
      onClose
    });
  }, [userId, navigate, toast, onClose]);

  const checkUpdatedFollows = useCallback(async (): Promise<void> => {
    if (!userId || userId === 'guest' || !accessToken) {
      setUpdatedFollowCount(0);
      return;
    }

    try {
      const followedData = await getHistoryData(FOLLOW_KEY, userId);
      if (followedData && Array.isArray(followedData)) {
        const updatedCount = (followedData as FollowedPost[]).filter(post => {
          if (!post.updated || !post.published) return false;
          const updated = new Date(post.updated);
          const published = new Date(post.published);
          return updated > published;
        }).length;
        setUpdatedFollowCount(updatedCount);
      } else {
        setUpdatedFollowCount(0);
      }
    } catch (error: any) {
      console.error('Error checking updated follows:', error);
      setUpdatedFollowCount(0);
    }
  }, [accessToken, userId, setUpdatedFollowCount]);

  return {
    handleOpenSearch,
    handleHistory,
    handleProfile,
    handleViewMore,
    login,
    logout,
    checkUpdatedFollows
  };
};