import React from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Heading,
  Text,
  useColorModeValue,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Divider
} from '@chakra-ui/react';
import LoginButton from '../components/Auth/LoginButton';
import { useAuth } from '../hooks/useAuthNew';

const LoginDemo: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const textColor = useColorModeValue('gray.900', 'white');
  const mutedTextColor = useColorModeValue('gray.600', 'gray.400');
  const cardBg = useColorModeValue('white', 'gray.800');

  return (
    <Container maxW="container.lg" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box textAlign="center">
          <Heading size="xl" color={textColor} mb={4}>
            <PERSON>gin Button Demo
          </Heading>
          <Text fontSize="lg" color={mutedTextColor}>
            <PERSON><PERSON><PERSON> kiểu nút đăng nhập khác nhau
          </Text>
          
          {isAuthenticated && (
            <Badge colorScheme="green" mt={4} p={2} borderRadius="md">
              Đã đăng nhập: {user?.name}
            </Badge>
          )}
        </Box>

        <Divider />

        {/* Login Buttons Grid */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {/* Icon Button */}
          <Card bg={cardBg} variant="outline">
            <CardBody>
              <VStack spacing={4}>
                <Heading size="md" color={textColor}>
                  Icon Button
                </Heading>
                <Text fontSize="sm" color={mutedTextColor} textAlign="center">
                  Nút icon đơn giản cho header/navbar
                </Text>
                <LoginButton variant="icon" size="lg" />
              </VStack>
            </CardBody>
          </Card>

          {/* Regular Button */}
          <Card bg={cardBg} variant="outline">
            <CardBody>
              <VStack spacing={4}>
                <Heading size="md" color={textColor}>
                  Regular Button
                </Heading>
                <Text fontSize="sm" color={mutedTextColor} textAlign="center">
                  Nút thông thường với text
                </Text>
                <LoginButton variant="button" size="md" w="200px" />
              </VStack>
            </CardBody>
          </Card>

          {/* Google Style Compact */}
          <Card bg={cardBg} variant="outline">
            <CardBody>
              <VStack spacing={4}>
                <Heading size="md" color={textColor}>
                  Google Style Compact
                </Heading>
                <Text fontSize="sm" color={mutedTextColor} textAlign="center">
                  Nút Google style gọn nhẹ
                </Text>
                <LoginButton 
                  variant="google-style" 
                  googleVariant="compact"
                />
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>

        <Divider />

        {/* Google Style Full Card */}
        <Box>
          <VStack spacing={6}>
            <Box textAlign="center">
              <Heading size="lg" color={textColor} mb={2}>
                Google Style Full Card
              </Heading>
              <Text fontSize="md" color={mutedTextColor}>
                Card đăng nhập giống Google One Tap
              </Text>
            </Box>

            <Box maxW="400px" mx="auto">
              <LoginButton 
                variant="google-style" 
                googleVariant="full"
              />
            </Box>
          </VStack>
        </Box>

        <Divider />

        {/* Usage Examples */}
        <Box>
          <Heading size="lg" color={textColor} mb={4}>
            Cách sử dụng
          </Heading>
          
          <VStack spacing={4} align="stretch">
            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="bold" color={textColor}>
                    Icon Button (Header/Navbar)
                  </Text>
                  <Text fontSize="sm" color={mutedTextColor} fontFamily="mono">
                    {`<LoginButton variant="icon" size="md" />`}
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="bold" color={textColor}>
                    Regular Button (Forms)
                  </Text>
                  <Text fontSize="sm" color={mutedTextColor} fontFamily="mono">
                    {`<LoginButton variant="button" size="lg" w="100%" />`}
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="bold" color={textColor}>
                    Google Style Compact (Quick Login)
                  </Text>
                  <Text fontSize="sm" color={mutedTextColor} fontFamily="mono">
                    {`<LoginButton variant="google-style" googleVariant="compact" />`}
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Text fontWeight="bold" color={textColor}>
                    Google Style Full (Landing Page)
                  </Text>
                  <Text fontSize="sm" color={mutedTextColor} fontFamily="mono">
                    {`<LoginButton variant="google-style" googleVariant="full" />`}
                  </Text>
                </VStack>
              </CardBody>
            </Card>
          </VStack>
        </Box>

        {/* Features */}
        <Box>
          <Heading size="lg" color={textColor} mb={4}>
            Tính năng
          </Heading>
          
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="blue">✅ Responsive</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    Tự động thích ứng với dark/light mode
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="green">✅ Loading States</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    Hiển thị trạng thái loading khi đăng nhập
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="purple">✅ Error Handling</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    Xử lý lỗi và hiển thị toast notifications
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="orange">✅ Auto Hide</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    Tự động ẩn khi user đã đăng nhập
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="cyan">✅ Floating Positions</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    4 vị trí: bottom-right, bottom-left, top-right, top-left
                  </Text>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg} variant="outline">
              <CardBody>
                <VStack align="start" spacing={2}>
                  <Badge colorScheme="pink">✅ Animations</Badge>
                  <Text fontSize="sm" color={mutedTextColor}>
                    Smooth animations với pulse effect
                  </Text>
                </VStack>
              </CardBody>
            </Card>
          </SimpleGrid>
        </Box>
      </VStack>
    </Container>
  );
};

export default LoginDemo;
