/* Optimized <PERSON>ag and Drop Styles */

/* Drag overlay performance */
.dnd-overlay {
  pointer-events: none !important;
  z-index: 9999 !important;
  position: fixed !important;
  will-change: transform !important;
  transform-origin: center center !important;
  contain: layout style paint !important;
}

/* Improve drag handle interaction */
.drag-handle {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.drag-handle:active {
  cursor: grabbing !important;
}

/* Sortable item styles */
.sortable-item {
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  will-change: transform, opacity;
}

.sortable-item.dragging {
  opacity: 0.4;
  z-index: 999;
  pointer-events: none;
}

/* Prevent text selection during drag */
.manga-uploader * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for specific elements */
.manga-uploader .selectable-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Smooth transitions */
.sortable-item:not(.dragging) {
  transition: transform 200ms ease, opacity 200ms ease;
}

/* Better mobile touch support */
@media (hover: none) and (pointer: coarse) {
  .drag-handle {
    min-width: 44px;
    min-height: 44px;
  }
}

/* Prevent scrolling during drag on mobile */
body.dragging {
  overflow: hidden;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
}

/* Improve performance with GPU acceleration */
.sortable-item,
.dnd-overlay,
.drag-handle {
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: transform;
  contain: layout style paint;
}

/* Force hardware acceleration for drag overlay */
.dnd-overlay {
  transform: translate3d(0, 0, 0) !important;
}

/* Smooth drag animations */
@keyframes dragStart {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

.sortable-item.dragging {
  animation: dragStart 0.2s ease-out;
}
