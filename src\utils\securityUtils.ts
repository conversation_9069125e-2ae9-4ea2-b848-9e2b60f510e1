import CryptoJS from 'crypto-js';
import { saveUserData, getUserData, deleteUserData } from './indexedDBUtils';

// Constants
const ENCRYPTION_KEY = import.meta.env.VITE_ENCRYPTION_KEY || 'ellenmylove';
const TOKEN_KEY = 'encrypted_token';
const USER_DATA_KEY = 'encrypted_user_data';
const SESSION_KEY = 'secure_session';

interface EncryptedData {
  value: string;
  timestamp: number;
}

interface SessionData {
  userData: any;
  token: string;
  refreshToken: string;
  timestamp: number;
  sessionId: string;
  expiresAt: number;
}

interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}

// Encryption helpers
export const encryptData = <T>(data: T): string | null => {
  try {
    if (!data) return null;
    const jsonString = JSON.stringify(data);
    return CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
  } catch (error: any) {
    console.error('Encryption error:', error);
    return null;
  }
};

export const decryptData = <T>(encryptedData: string): T | null => {
  try {
    if (!encryptedData) return null;
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const decryptedString = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedString);
  } catch (error: any) {
    console.error('Decryption error:', error);
    return null;
  }
};

// Token management with encryption
export const encryptAndStoreToken = async (token: string): Promise<boolean> => {
  try {
    if (!token) return false;
    const encryptedToken = encryptData(token);
    if (!encryptedToken) return false;
    
    await saveUserData(TOKEN_KEY, { value: encryptedToken, timestamp: Date.now() });
    return true;
  } catch (error: any) {
    console.error('Error storing encrypted token:', error);
    return false;
  }
};

export const getAndDecryptToken = async (): Promise<string | null> => {
  try {
    const tokenData = await getUserData(TOKEN_KEY);
    if (!tokenData?.value) return null;
    return decryptData<string>(tokenData.value);
  } catch (error: any) {
    console.error('Error retrieving encrypted token:', error);
    return null;
  }
};

// User data encryption
export const encryptAndStoreUserData = async <T>(userData: T): Promise<boolean> => {
  try {
    if (!userData) return false;
    const encryptedData = encryptData(userData);
    if (!encryptedData) return false;
    
    await saveUserData(USER_DATA_KEY, { value: encryptedData, timestamp: Date.now() });
    return true;
  } catch (error: any) {
    console.error('Error storing encrypted user data:', error);
    return false;
  }
};

export const getAndDecryptUserData = async <T>(): Promise<T | null> => {
  try {
    const userData = await getUserData(USER_DATA_KEY);
    if (!userData?.value) return null;
    return decryptData<T>(userData.value);
  } catch (error: any) {
    console.error('Error retrieving encrypted user data:', error);
    return null;
  }
};

// Clear encrypted data
export const clearEncryptedData = async (): Promise<boolean> => {
  try {
    await Promise.all([
      deleteUserData(TOKEN_KEY),
      deleteUserData(USER_DATA_KEY),
      deleteUserData(SESSION_KEY)
    ]);
    return true;
  } catch (error: any) {
    console.error('Error clearing encrypted data:', error);
    return false;
  }
};

// Data compression
export const compressData = <T>(data: T): string | null => {
  try {
    if (!data) return null;
    const jsonString = JSON.stringify(data);
    return btoa(jsonString); // Simple base64 encoding for now
  } catch (error: any) {
    console.error('Compression error:', error);
    return null;
  }
};

export const decompressData = <T>(compressedData: string): T | null => {
  try {
    if (!compressedData) return null;
    const jsonString = atob(compressedData);
    return JSON.parse(jsonString);
  } catch (error: any) {
    console.error('Decompression error:', error);
    return null;
  }
};

// Session management
export const createSecureSession = async (
  userData: any,
  token: string,
  refreshToken: string
): Promise<boolean> => {
  try {
    console.log('Creating secure session...', {
      hasUserData: !!userData,
      hasToken: !!token,
      hasRefreshToken: !!refreshToken
    });

    const sessionData: SessionData = {
      userData,
      token,
      refreshToken,
      timestamp: Date.now(),
      sessionId: CryptoJS.lib.WordArray.random(16).toString(),
      expiresAt: Date.now() + (1 * 60 * 60 * 1000) // 1 hour
    };
    
    const encryptedSession = encryptData(sessionData);
    if (!encryptedSession) {
      console.error('Failed to encrypt session data');
      return false;
    }
    
    await saveUserData(SESSION_KEY, { value: encryptedSession, timestamp: Date.now() });
    console.log('Secure session created successfully');
    return true;
  } catch (error: any) {
    console.error('Error creating secure session:', error);
    return false;
  }
};

export const validateSession = async (): Promise<boolean> => {
  try {
    console.log('Validating session...');
    const sessionData = await getUserData(SESSION_KEY);
    if (!sessionData?.value) {
      console.log('No secure session found');
      return false;
    }
    
    const session = decryptData<SessionData>(sessionData.value);
    if (!session) {
      console.log('Failed to decrypt session data');
      return false;
    }

    console.log('Session data:', {
      hasUserData: !!session.userData,
      hasToken: !!session.token,
      hasRefreshToken: !!session.refreshToken,
      expiresAt: session.expiresAt ? new Date(session.expiresAt).toISOString() : null,
      currentTime: new Date().toISOString()
    });
    
    // Check if session is expired
    if (Date.now() >= session.expiresAt) {
      console.log('Session expired, attempting to refresh token');
      // Try to refresh token if we have one
      if (session.refreshToken) {
        try {
          console.log('Attempting to refresh token...');
          const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refreshToken: session.refreshToken }),
          });
          
          if (response.ok) {
            console.log('Token refresh successful');
            const { accessToken, refreshToken } = await response.json() as TokenResponse;
            // Update session with new tokens
            const success = await createSecureSession(session.userData, accessToken, refreshToken);
            if (success) {
              console.log('Session updated with new tokens');
              return true;
            }
          }
          console.log('Token refresh failed');
        } catch (error: any) {
          console.error('Error refreshing token:', error);
        }
      } else {
        console.log('No refresh token available');
      }
      
      // If refresh failed or no refresh token, clear session
      console.log('Clearing expired session');
      await clearEncryptedData();
      return false;
    }
    
    console.log('Session is valid');
    return true;
  } catch (error: any) {
    console.error('Error validating session:', error);
    return false;
  }
};

// Two-factor authentication helpers
export const generateTOTP = async (secret: string): Promise<string | null> => {
  try {
    const timestamp = Math.floor(Date.now() / 30000); // 30-second window
    const message = timestamp.toString(16).padStart(16, '0');
    const key = CryptoJS.enc.Hex.parse(secret);
    const hash = CryptoJS.HmacSHA1(message, key);
    const offset = hash.words[hash.words.length - 1] & 0xf;
    const code = ((hash.words[offset >> 2] >> (24 - (offset & 0x3) * 8)) & 0x7fffffff) % 1000000;
    return code.toString().padStart(6, '0');
  } catch (error: any) {
    console.error('Error generating TOTP:', error);
    return null;
  }
};

export const verifyTOTP = async (secret: string, code: string): Promise<boolean> => {
  try {
    const generatedCode = await generateTOTP(secret);
    return generatedCode === code;
  } catch (error: any) {
    console.error('Error verifying TOTP:', error);
    return false;
  }
}; 