@import '@glidejs/glide/dist/css/glide.core.min.css';

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  opacity: 0;
  animation: fadeIn 300ms ease-in forwards;
}

/* Apple-style Page Transitions */
.apple-fade-enter {
  opacity: 0;
  transform: translateY(8px);
  filter: blur(4px);
}
.apple-fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  filter: blur(0px);
  transition:
    opacity 300ms cubic-bezier(0.25, 0.1, 0.25, 1),
    transform 400ms cubic-bezier(0.25, 0.1, 0.25, 1),
    filter 300ms cubic-bezier(0.25, 0.1, 0.25, 1);
}
.apple-fade-exit {
  opacity: 1;
  transform: translateY(0);
  filter: blur(0px);
}
.apple-fade-exit-active {
  opacity: 0;
  transform: translateY(-8px);
  filter: blur(4px);
  transition:
    opacity 300ms cubic-bezier(0.25, 0.1, 0.25, 1),
    transform 400ms cubic-bezier(0.25, 0.1, 0.25, 1),
    filter 300ms cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* HomePage Card Animations */
@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(30px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
}
}

.navbar {
      background-color: rgba(0, 0, 0, 0.8) !important;
      backdrop-filter: blur(10px);
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      transition: background-color 0.3s ease;
}

.content-wrapper {
  position: relative;
  margin-top: 60px;
  min-height: calc(100vh - 60px);
}

@media (min-width: 1400px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1440px;
    }
}

.loading-overlay {
  position: fixed;
  inset: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0.25), rgba(0,0,0,0.55));
  backdrop-filter: blur(8px);
  z-index: 1050;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

.not-found {
  font-size: 1.2rem;
  text-align: center;
  margin-top: 3rem;
  color: #555;
}

.card-description {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 90%;
  color: white;
  padding: 10px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  border-radius: var(--bs-card-border-radius);
  font-size: 0.8rem;
  font-weight: 500;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.8) 100%
  );
  backdrop-filter: blur(2px);
}

.card-custom-hover {
  position: relative;
  overflow: hidden;
  border-radius: var(--bs-card-border-radius);
  opacity: 0;
  animation: cardAppear 0.6s ease-out forwards;
  
  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
  &:nth-child(5) { animation-delay: 0.5s; }
  &:nth-child(n+6) { animation-delay: 0.6s; }

   .card-img {
      height: 320px;
      width: 100%;
      filter: brightness(0.9);
      -webkit-filter: brightness(0.9);
    transition: all 0.4s ease-out;
    }

  @media screen and (max-width: 768px) {
    .card-img {
      height: 280px;
    }
  }
 
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;

    .card-img {
      filter: blur(5px) brightness(0.5);
      -webkit-filter: blur(5px) brightness(0.5);
      border-radius: var(--bs-card-border-radius);
      transform: scale(1.05);
    }

    .card-description {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.card-custom-title {
  margin-top: 0.65rem;
  padding: 0 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 2.5rem;
  transition: color 0.3s ease;
}

@media (any-pointer: fine) and (min-width:640px) {
  body {
    overflow-y: scroll !important;
  }

  ::-webkit-scrollbar {
      height: 10px;
    width: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
      background-color: #adb5bd;
    border-radius: 2rem;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #adb5bd;
  }
}

.separator {
  max-width: 100%;
  margin: 0 auto;
  overflow-x: hidden;
  
  img {
max-width: 100%;
height: auto;
display: block;
    margin: 0 auto;
    object-fit: contain;
box-sizing: border-box;
}
}

body {
  background: #181A1B !important;
  color: #E0E0E0;
  font-family: 'Inter', 'Roboto', Arial, sans-serif;
}

::-webkit-scrollbar {
  width: 8px;
  background: #23272A;
}
::-webkit-scrollbar-thumb {
  background: #393E46;
  border-radius: 8px;
}

.card, .chakra-card, .chakra-box {
  background: #23272A !important;
  border-radius: 18px !important;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.25) !important;
}