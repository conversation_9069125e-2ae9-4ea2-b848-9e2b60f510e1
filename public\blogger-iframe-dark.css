/* Blogger Comment Iframe Dark Mode Styles */

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  body.EIlDfe { 
    background-color: #131313 !important; 
    color: #ffffff !important;
  }
  
  .EIlDfe * {
    background-color: #131313 !important; 
    color: #ffffff !important;
  }
  
  .EIlDfe input, 
  .EIlDfe textarea,
  .EIlDfe select { 
    background-color: #1a1a1a !important; 
    color: #ffffff !important;
    border: 1px solid #333333 !important;
  }
  
  .EIlDfe button,
  .EIlDfe input[type="submit"],
  .EIlDfe input[type="button"] {
    background-color: #333333 !important;
    color: #ffffff !important;
    border: 1px solid #555555 !important;
  }
  
  .EIlDfe a {
    color: #00d4ff !important;
  }
  
  .EIlDfe input::placeholder,
  .EIlDfe textarea::placeholder {
    color: #a0aec0 !important;
  }
}

/* Light Mode */
@media (prefers-color-scheme: light) {
  body.EIlDfe { 
    background-color: #f4f4f4 !important; 
    color: #1a202c !important;
  }
  
  .EIlDfe * {
    background-color: #f4f4f4 !important; 
    color: #1a202c !important;
  }
  
  .EIlDfe input, 
  .EIlDfe textarea,
  .EIlDfe select { 
    background-color: #ffffff !important; 
    color: #1a202c !important;
    border: 1px solid #e2e8f0 !important;
  }
  
  .EIlDfe button,
  .EIlDfe input[type="submit"],
  .EIlDfe input[type="button"] {
    background-color: #ffffff !important;
    color: #1a202c !important;
    border: 1px solid #e2e8f0 !important;
  }
  
  .EIlDfe a {
    color: #3182ce !important;
  }
  
  .EIlDfe input::placeholder,
  .EIlDfe textarea::placeholder {
    color: #718096 !important;
  }
}

/* Force styles regardless of media query */
.dark-mode body.EIlDfe { 
  background-color: #131313 !important; 
  color: #ffffff !important;
}

.light-mode body.EIlDfe { 
  background-color: #f4f4f4 !important; 
  color: #1a202c !important;
}
