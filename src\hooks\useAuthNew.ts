import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@chakra-ui/react';
import { authService, UserData } from '../services/authService';
import useUserStore from '../store/useUserStore';

export interface AuthState {
  user: UserData | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

export interface AuthActions {
  login: (accessToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
  forceRefresh: () => void;
  syncData: () => Promise<void>;
  exportData: () => Promise<void>;
  deleteAccount: () => Promise<void>;
}

export interface UseAuthReturn extends AuthState, AuthActions {}

export const useAuth = (): UseAuthReturn => {
  const [state, setState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null
  });
  const [refreshKey, setRefreshKey] = useState(0);

  const navigate = useNavigate();
  const toast = useToast();
  const { setUser, logout: userStoreLogout, initializeUser, clearUser } = useUserStore();

  // Update state helper
  const updateState = useCallback((updates: Partial<AuthState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Initialize auth on mount with debounce and retry
  useEffect(() => {
    let isCancelled = false;
    let timeoutId: NodeJS.Timeout;

    const initAuth = async (retryCount = 0) => {
      if (isCancelled) return;

      try {
        console.log(`[useAuthNew] Starting auth initialization (attempt ${retryCount + 1})`);
        updateState({ isLoading: true, error: null });

        // Add small delay to avoid race conditions
        await new Promise(resolve => setTimeout(resolve, 100));

        if (isCancelled) return;

        const user = await authService.getCurrentUser();

        if (isCancelled) return;

        if (user) {
          // Get token from authService and sync with userStore
          const tokens = await authService.tokenManager.getTokens();
          const accessToken = tokens?.accessToken || '';

          console.log('[useAuthNew] Syncing user and token:', {
            hasUser: !!user,
            hasToken: !!accessToken,
            tokenLength: accessToken.length
          });

          if (!isCancelled) {
            await setUser(user, accessToken);
            updateState({
              user,
              isAuthenticated: true,
              isLoading: false
            });
          }
        } else {
          if (!isCancelled) {
            updateState({
              user: null,
              isAuthenticated: false,
              isLoading: false
            });
          }
        }
      } catch (error) {
        console.error(`Auth initialization error (attempt ${retryCount + 1}):`, error);

        // Retry up to 2 times with exponential backoff
        if (retryCount < 2 && !isCancelled) {
          const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
          console.log(`[useAuthNew] Retrying auth initialization in ${delay}ms...`);

          timeoutId = setTimeout(() => {
            if (!isCancelled) {
              initAuth(retryCount + 1);
            }
          }, delay);
        } else if (!isCancelled) {
          updateState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: 'Failed to initialize authentication'
          });
        }
      }
    };

    // Debounce initialization to avoid rapid calls
    timeoutId = setTimeout(() => {
      if (!isCancelled) {
        initAuth();
      }
    }, 50);

    return () => {
      isCancelled = true;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [setUser, updateState]);

  // Login function
  const login = useCallback(async (accessToken: string) => {
    try {
      updateState({ isLoading: true, error: null });

      const userData = await authService.login(accessToken);

      // Update store - don't call initializeUser immediately to avoid race condition
      console.log('[useAuthNew] Setting user data and access token...');
      await setUser(userData, accessToken);
      console.log('[useAuthNew] User data set successfully, skipping initializeUser to avoid race condition');

      updateState({
        user: userData,
        isAuthenticated: true,
        isLoading: false
      });

      // Force refresh all components
      setRefreshKey(prev => prev + 1);

      toast({
        title: 'Đăng nhập thành công',
        description: `Chào mừng ${userData.name}!`,
        status: 'success',
        duration: 3000,
        isClosable: true
      });

      // No automatic redirect - let components handle their own navigation
      console.log('✅ Login successful, staying on current page');
    } catch (error: any) {
      console.error('Login error:', error);

      const errorMessage = error.message || 'Đăng nhập thất bại';
      updateState({
        isLoading: false,
        error: errorMessage
      });

      toast({
        title: 'Lỗi đăng nhập',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  }, [setUser, initializeUser, updateState, toast, navigate]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      console.log('[useAuthNew] 🚪 Starting logout process...');
      updateState({ isLoading: true, error: null });

      const userId = state.user?.id;
      console.log('[useAuthNew] User ID for logout:', userId);

      console.log('[useAuthNew] Calling authService.logout...');
      await authService.logout(userId);

      console.log('[useAuthNew] Calling userStoreLogout...');
      await userStoreLogout();

      console.log('[useAuthNew] Updating auth state to logged out...');
      updateState({
        user: null,
        isAuthenticated: false,
        isLoading: false
      });

      toast({
        title: 'Đăng xuất thành công',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      navigate('/'); // Redirect to home or login page after logout
    } catch (error: any) {
      console.error('Logout error:', error);

      const errorMessage = error.message || 'Đăng xuất thất bại';
      updateState({
        isLoading: false,
        error: errorMessage
      });

      toast({
        title: 'Lỗi đăng xuất',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  }, [state.user?.id, userStoreLogout, updateState, toast, navigate]);

  const syncData = useCallback(async () => {
    if (!state.isAuthenticated) {
      toast({
        title: 'Lỗi đồng bộ',
        description: 'Bạn cần đăng nhập để đồng bộ dữ liệu.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    updateState({ isLoading: true, error: null });
    try {
      // Simulate data sync
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast({
        title: 'Đồng bộ thành công',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('Sync data error:', error);
      toast({
        title: 'Lỗi đồng bộ',
        description: error.message || 'Không thể đồng bộ dữ liệu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      updateState({ isLoading: false });
    }
  }, [state.isAuthenticated, updateState, toast]);

  const exportData = useCallback(async () => {
    if (!state.isAuthenticated) {
      toast({
        title: 'Lỗi xuất dữ liệu',
        description: 'Bạn cần đăng nhập để xuất dữ liệu.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    updateState({ isLoading: true, error: null });
    try {
      // Simulate data export
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast({
        title: 'Xuất dữ liệu thành công',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('Export data error:', error);
      toast({
        title: 'Lỗi xuất dữ liệu',
        description: error.message || 'Không thể xuất dữ liệu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      updateState({ isLoading: false });
    }
  }, [state.isAuthenticated, updateState, toast]);

  const deleteAccount = useCallback(async () => {
    if (!state.isAuthenticated) {
      toast({
        title: 'Lỗi xóa tài khoản',
        description: 'Bạn cần đăng nhập để xóa tài khoản.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    updateState({ isLoading: true, error: null });
    try {
      // Simulate account deletion
      await new Promise(resolve => setTimeout(resolve, 2000));
      // After successful deletion, log out the user and clear local state
      await authService.logout(state.user?.id);
      await userStoreLogout();
      updateState({
        user: null,
        isAuthenticated: false,
        isLoading: false
      });
      toast({
        title: 'Xóa tài khoản thành công',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      navigate('/'); // Redirect after deletion
    } catch (error: any) {
      console.error('Delete account error:', error);
      toast({
        title: 'Lỗi xóa tài khoản',
        description: error.message || 'Không thể xóa tài khoản',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      updateState({ isLoading: false });
    }
  }, [state.isAuthenticated, state.user?.id, userStoreLogout, updateState, toast, navigate]);

  const refreshAuth = useCallback(async () => {
    // Implement actual token refresh logic if needed
    // For now, just re-initialize user state
    console.log('[useAuthNew] Refreshing auth state...');
    setRefreshKey(prev => prev + 1);
  }, []);

  const forceRefresh = useCallback(() => {
    console.log('[useAuthNew] Forcing full refresh...');
    setRefreshKey(prev => prev + 1);
  }, []);

  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  return {
    ...state,
    login,
    logout,
    refreshAuth,
    clearError,
    forceRefresh,
    syncData,
    exportData,
    deleteAccount,
  };
};
