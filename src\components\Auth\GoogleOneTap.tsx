import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  Text,
  HStack,
  VStack,
  Avatar,
  useColorMode,
  IconButton,
  Fade,
  useToast
} from '@chakra-ui/react';
import { CloseIcon } from '@chakra-ui/icons';
import { useAuth } from '../../hooks/useAuthNew';
import { blogConfig } from '../../config';

interface GoogleOneTapProps {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  disabled?: boolean;
}

interface UserProfile {
  name: string;
  email: string;
  picture: string;
  sub: string;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          prompt: (callback?: (notification: any) => void) => void;
          renderButton: (element: HTMLElement, config: any) => void;
          disableAutoSelect: () => void;
          cancel: () => void;
        };
      };
    };
  }
}

const GoogleOneTap: React.FC<GoogleOneTapProps> = ({
  onSuccess,
  onError,
  disabled = false
}) => {
  const { login, isAuthenticated } = useAuth();
  const toast = useToast();
  const { colorMode } = useColorMode();
  const initRef = useRef(false);

  // State for custom UI
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const isDark = colorMode === 'dark';

  useEffect(() => {
    // Don't show One Tap if already authenticated or disabled
    if (isAuthenticated || disabled || initRef.current) {
      return;
    }

    // Check if we should show custom prompt (e.g., returning user)
    const lastUser = localStorage.getItem('lastGoogleUser');
    if (lastUser) {
      try {
        const userData = JSON.parse(lastUser);
        setUserProfile(userData);
        setShowCustomPrompt(true);
        initRef.current = true;
        return;
      } catch (error) {
        console.warn('[OneTap] Invalid stored user data');
        localStorage.removeItem('lastGoogleUser');
      }
    }

    // Initialize Google One Tap for new users
    const initializeOneTap = () => {
      if (!window.google?.accounts?.id) {
        console.log('[OneTap] Google Identity Services not loaded yet, retrying...');
        setTimeout(initializeOneTap, 500);
        return;
      }

      if (initRef.current) return;
      initRef.current = true;

      console.log('[OneTap] Initializing Google One Tap...');
      console.log('[OneTap] Client ID:', import.meta.env.VITE_GOOGLE_CLIENT_ID);
      console.log('[OneTap] Current origin:', window.location.origin);
      console.log('[OneTap] Current hostname:', window.location.hostname);

      try {
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleCredentialResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          context: 'signin',
          ux_mode: 'popup',
          use_fedcm_for_prompt: false,
          itp_support: true
        });

        // Show native One Tap for first-time users
        window.google.accounts.id.prompt((notification: any) => {
          console.log('[OneTap] Prompt notification:', notification);

          if (notification.isNotDisplayed()) {
            const reason = notification.getNotDisplayedReason();
            console.log('[OneTap] Prompt not displayed:', reason);

            // Show custom fallback for unsupported cases
            if (reason === 'browser_not_supported' || reason === 'invalid_client') {
              setShowCustomPrompt(true);
            }
          }
        });

      } catch (error) {
        console.error('[OneTap] Error initializing:', error);
        // Show custom prompt as fallback
        setShowCustomPrompt(true);
      }
    };

    // Load Google Identity Services script
    if (!document.querySelector('script[src*="accounts.google.com"]')) {
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = initializeOneTap;
      script.onerror = () => {
        // Fallback to custom prompt if script fails
        setShowCustomPrompt(true);
      };
      document.head.appendChild(script);
    } else {
      initializeOneTap();
    }

    // Cleanup function
    return () => {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.cancel();
      }
    };
  }, [isAuthenticated, disabled]);

  // Listen for manual trigger
  useEffect(() => {
    const handleShowOneTap = () => {
      const lastUser = localStorage.getItem('lastGoogleUser');
      if (lastUser) {
        try {
          const userData = JSON.parse(lastUser);
          setUserProfile(userData);
          setShowCustomPrompt(true);
        } catch (error) {
          console.warn('[OneTap] Invalid stored user data');
        }
      }
    };

    window.addEventListener('showOneTap', handleShowOneTap);
    return () => window.removeEventListener('showOneTap', handleShowOneTap);
  }, []);

  const handleCredentialResponse = async (response: any) => {
    try {
      console.log('[OneTap] Credential response received');

      if (!response.credential) {
        throw new Error('No credential in response');
      }

      // Decode JWT token to get user info
      const payload = JSON.parse(atob(response.credential.split('.')[1]));
      console.log('[OneTap] JWT payload:', payload);

      // Store user info for future custom prompts
      const userData: UserProfile = {
        name: payload.name,
        email: payload.email,
        picture: payload.picture,
        sub: payload.sub
      };

      localStorage.setItem('lastGoogleUser', JSON.stringify(userData));
      setUserProfile(userData);

      // Try to exchange credential for access token
      await exchangeCredentialForToken(response.credential);

    } catch (error) {
      console.error('[OneTap] Error handling credential:', error);

      toast({
        title: 'Đăng nhập thất bại',
        description: 'Có lỗi xảy ra khi đăng nhập với Google One Tap',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      onError?.(error);
    }
  };

  // Handle custom prompt login
  const handleCustomLogin = async () => {
    if (!userProfile) return;

    setIsLoading(true);
    try {
      // Trigger Google OAuth flow for returning user
      // This will open the standard OAuth popup
      const redirectUri = blogConfig.redirectUri || window.location.origin;

      console.log('[OneTap] Using redirect URI:', redirectUri);
      console.log('[OneTap] BlogConfig redirect URI:', blogConfig.redirectUri);

      window.open(
        `https://accounts.google.com/oauth/authorize?` +
        `client_id=${import.meta.env.VITE_GOOGLE_CLIENT_ID}&` +
        `redirect_uri=${redirectUri}&` +
        `response_type=token&` +
        `scope=openid email profile https://www.googleapis.com/auth/blogger&` +
        `login_hint=${userProfile.email}`,
        'google-login',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for OAuth completion
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'GOOGLE_OAUTH_SUCCESS') {
          window.removeEventListener('message', handleMessage);
          login(event.data.accessToken);
          setShowCustomPrompt(false);
          onSuccess?.();
        }
      };

      window.addEventListener('message', handleMessage);

    } catch (error) {
      console.error('[OneTap] Custom login error:', error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  };

  const exchangeCredentialForToken = async (credential: string) => {
    try {
      console.log('[OneTap] Exchanging credential for access token...');

      // Decode JWT to get user info
      const payload = JSON.parse(atob(credential.split('.')[1]));
      console.log('[OneTap] JWT payload:', payload);

      // Note: JWT to OAuth token exchange requires client_secret which cannot be safely stored in frontend
      // Instead, we'll verify the JWT and provide user identification only
      try {
        // Verify the JWT is valid by calling Google's tokeninfo endpoint
        const verifyResponse = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${credential}`);

        if (verifyResponse.ok) {
          const verifiedPayload = await verifyResponse.json();
          console.log('[OneTap] JWT verified:', verifiedPayload);

          // Store verified user info for personalization
          localStorage.setItem('oneTapUser', JSON.stringify({
            name: payload.name,
            email: payload.email,
            picture: payload.picture,
            sub: payload.sub,
            verified: true,
            timestamp: Date.now()
          }));

          toast({
            title: 'Nhận diện thành công',
            description: `Chào ${payload.name}! Để sử dụng đầy đủ tính năng Blogger, vui lòng đăng nhập bằng nút "Đăng nhập".`,
            status: 'info',
            duration: 7000,
            isClosable: true,
          });

          onSuccess?.();
          return;
        }
      } catch (verifyError) {
        console.warn('[OneTap] JWT verification failed:', verifyError);
      }

      // Method 3: Complete fallback
      throw new Error('Unable to establish session with One Tap credential');

    } catch (error) {
      console.error('[OneTap] Error exchanging credential:', error);

      // Show helpful message
      toast({
        title: 'One Tap không khả dụng',
        description: 'Vui lòng sử dụng nút "Đăng nhập" để truy cập đầy đủ tính năng',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });

      onError?.(error);
    }
  };

  // Render custom Reddit-style One Tap prompt
  if (!showCustomPrompt || !userProfile) {
    return null; // Native One Tap or no prompt
  }

  return (
    <Fade in={showCustomPrompt}>
      <Box
        position="fixed"
        top="20px"
        right="20px"
        zIndex={9999}
        maxW="380px"
        bg={isDark ? '#1a1a1b' : '#ffffff'}
        border="1px solid"
        borderColor={isDark ? '#343536' : '#edeff1'}
        borderRadius="12px"
        boxShadow={isDark
          ? "0 8px 32px rgba(0,0,0,0.4), 0 2px 8px rgba(0,0,0,0.2)"
          : "0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08)"
        }
        p={4}
        backdropFilter="blur(16px)"
        transform="translateY(0)"
        animation="slideInFromTop 0.3s ease-out"
        sx={{
          '@keyframes slideInFromTop': {
            '0%': {
              transform: 'translateY(-20px)',
              opacity: 0
            },
            '100%': {
              transform: 'translateY(0)',
              opacity: 1
            }
          }
        }}
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: isDark ? 'rgba(26,26,27,0.95)' : 'rgba(255,255,255,0.95)',
          borderRadius: '12px',
          zIndex: -1
        }}
      >
        <VStack spacing={4} align="stretch">
          {/* Header */}
          <HStack justify="space-between" align="center">
            <HStack spacing={3}>
              <Box
                w="24px"
                h="24px"
                bg="linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335)"
                borderRadius="6px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Text fontSize="12px" fontWeight="bold" color="white">
                  G
                </Text>
              </Box>
              <Text
                fontSize="14px"
                fontWeight="600"
                color={isDark ? '#d7dadc' : '#1c1c1c'}
              >
                Continue with Google
              </Text>
            </HStack>

            <IconButton
              aria-label="Close"
              icon={<CloseIcon />}
              size="sm"
              variant="ghost"
              color={isDark ? '#818384' : '#878a8c'}
              onClick={() => {
                setShowCustomPrompt(false);
                localStorage.removeItem('lastGoogleUser');
                onError?.(new Error('User dismissed One Tap'));
              }}
              _hover={{
                bg: isDark ? '#272729' : '#f6f7f8'
              }}
            />
          </HStack>

          {/* User Info */}
          <HStack spacing={3} p={3} bg={isDark ? '#272729' : '#f6f7f8'} borderRadius="8px">
            <Avatar
              size="md"
              src={userProfile.picture}
              name={userProfile.name}
              border="2px solid"
              borderColor={isDark ? '#343536' : '#edeff1'}
            />
            <VStack align="start" spacing={0} flex={1}>
              <Text
                fontSize="14px"
                fontWeight="600"
                color={isDark ? '#d7dadc' : '#1c1c1c'}
                noOfLines={1}
              >
                {userProfile.name}
              </Text>
              <Text
                fontSize="12px"
                color={isDark ? '#818384' : '#878a8c'}
                noOfLines={1}
              >
                {userProfile.email}
              </Text>
            </VStack>
          </HStack>

          {/* Action Buttons */}
          <VStack spacing={2}>
            <Button
              onClick={handleCustomLogin}
              isLoading={isLoading}
              loadingText="Signing in..."
              w="100%"
              h="40px"
              bg={isDark ? '#0079d3' : '#0079d3'}
              color="white"
              borderRadius="20px"
              fontSize="14px"
              fontWeight="600"
              _hover={{
                bg: isDark ? '#1484d6' : '#1484d6',
                transform: 'translateY(-1px)'
              }}
              _active={{
                transform: 'translateY(0)'
              }}
              transition="all 0.2s"
            >
              Continue as {userProfile.name.split(' ')[0]}
            </Button>

            <Button
              onClick={() => {
                setShowCustomPrompt(false);
                localStorage.removeItem('lastGoogleUser');
                // Trigger regular login flow
                window.dispatchEvent(new CustomEvent('triggerRegularLogin'));
              }}
              w="100%"
              h="36px"
              variant="ghost"
              color={isDark ? '#818384' : '#878a8c'}
              fontSize="13px"
              borderRadius="18px"
              _hover={{
                bg: isDark ? '#272729' : '#f6f7f8'
              }}
            >
              Use different account
            </Button>
          </VStack>

          {/* Footer */}
          <Text
            fontSize="11px"
            color={isDark ? '#818384' : '#878a8c'}
            textAlign="center"
            lineHeight="1.3"
          >
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </VStack>
      </Box>
    </Fade>
  );
};

export default GoogleOneTap;
