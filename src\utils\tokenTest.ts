// Token persistence test utility
import { authService } from '../services/authService';

export const testTokenPersistence = async () => {
  console.log('=== Secure Token Persistence Test ===');

  try {
    // Test 1: Check if tokens exist in localStorage (legacy)
    const localStorageTokens = localStorage.getItem('auth_tokens');
    console.log('1. LocalStorage tokens (legacy):', localStorageTokens ? 'Found' : 'Not found');

    if (localStorageTokens) {
      const parsed = JSON.parse(localStorageTokens);
      console.log('   - Access token:', parsed.accessToken ? `${parsed.accessToken.substring(0, 20)}...` : 'None');
      console.log('   - Expires at:', parsed.expiresAt ? new Date(parsed.expiresAt).toLocaleString() : 'None');
      console.log('   - Is expired:', parsed.expiresAt ? Date.now() >= parsed.expiresAt : 'Unknown');
    }

    // Test 1.5: Check secure storage
    const { secureStorage } = await import('./secureStorage');
    const secureTokens = await secureStorage.getItem('auth_tokens', { keyName: 'auth', sessionKey: true });
    console.log('1.5. Secure storage tokens:', secureTokens ? 'Found' : 'Not found');

    if (secureTokens) {
      console.log('   - Access token:', secureTokens.accessToken ? `${secureTokens.accessToken.substring(0, 20)}...` : 'None');
      console.log('   - Expires at:', secureTokens.expiresAt ? new Date(secureTokens.expiresAt).toLocaleString() : 'None');
      console.log('   - Is expired:', secureTokens.expiresAt ? Date.now() >= secureTokens.expiresAt : 'Unknown');
    }
    
    // Test 2: Check AuthService token manager
    const tokens = await authService.tokenManager.getTokens();
    console.log('2. AuthService tokens:', tokens ? 'Found' : 'Not found');
    
    if (tokens) {
      console.log('   - Access token:', tokens.accessToken ? `${tokens.accessToken.substring(0, 20)}...` : 'None');
      console.log('   - Expires at:', tokens.expiresAt ? new Date(tokens.expiresAt).toLocaleString() : 'None');
    }
    
    // Test 3: Check token validity
    const isValid = await authService.tokenManager.isTokenValid();
    console.log('3. Token validity:', isValid ? 'Valid' : 'Invalid');
    
    // Test 4: Try to get current user
    const user = await authService.getCurrentUser();
    console.log('4. Current user:', user ? `${user.name} (${user.email})` : 'None');
    
    return {
      hasLocalStorageTokens: !!localStorageTokens,
      hasSecureStorageTokens: !!secureTokens,
      hasAuthServiceTokens: !!tokens,
      isTokenValid: isValid,
      hasCurrentUser: !!user
    };
    
  } catch (error) {
    console.error('Token test error:', error);
    return {
      hasLocalStorageTokens: false,
      hasSecureStorageTokens: false,
      hasAuthServiceTokens: false,
      isTokenValid: false,
      hasCurrentUser: false,
      error: error.message
    };
  }
};

// Database debug functions
export const debugDatabase = async () => {
  const { debugDatabase } = await import('./indexedDBUtils');
  await debugDatabase();
};

export const recreateDatabase = async () => {
  const { recreateDatabase } = await import('./indexedDBUtils');
  await recreateDatabase();
  console.log('Database recreated. Please refresh the page.');
};

// Add to window for easy testing
if (typeof window !== 'undefined') {
  (window as any).testTokenPersistence = testTokenPersistence;
  (window as any).debugDatabase = debugDatabase;
  (window as any).recreateDatabase = recreateDatabase;
}
