import React, { createContext, useContext, useEffect, ReactNode, useState } from 'react';
// import { useAuth, UseAuthReturn } from '../hooks/useAuthNew'; // REMOVED - causing useAuth spam
import { Spinner, Center, Box } from '@chakra-ui/react';
import useUserStore from '../store/useUserStore';

// Simplified AuthContextType to avoid useAuth hook
interface AuthContextType {
  user: any;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  login: (accessToken: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
  forceRefresh: () => void;
  syncData: () => Promise<void>;
  exportData: () => Promise<void>;
  deleteAccount: () => Promise<void>;
  uploadToken: string | null;
  setUploadToken: (token: string | null) => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider component - using store instead of useAuth hook
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Use store directly to avoid useAuth hook spam
  const {
    user,
    isAuthenticated,
    logout: storeLogout,
    initializeUser
  } = useUserStore();

  const [uploadToken, setUploadToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dummy implementations to satisfy interface
  const login = async (accessToken: string) => {
    // Implementation would go here
    console.log('AuthProvider login called with token:', accessToken);
  };

  const refreshAuth = async () => {
    // Implementation would go here
    console.log('AuthProvider refreshAuth called');
  };

  const clearError = () => {
    setError(null);
  };

  const forceRefresh = () => {
    // Implementation would go here
    console.log('AuthProvider forceRefresh called');
  };

  const syncData = async () => {
    // Implementation would go here
    console.log('AuthProvider syncData called');
  };

  const exportData = async () => {
    // Implementation would go here
    console.log('AuthProvider exportData called');
  };

  const deleteAccount = async () => {
    // Implementation would go here
    console.log('AuthProvider deleteAccount called');
  };

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    error,
    login,
    logout: storeLogout,
    refreshAuth,
    clearError,
    forceRefresh,
    syncData,
    exportData,
    deleteAccount,
    uploadToken,
    setUploadToken
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

// HOC for protected routes
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback
}) => {
  // Use store directly to avoid useAuth hook spam
  const { isAuthenticated } = useUserStore();
  const isLoading = false; // Simplified for now

  if (isLoading) {
    return (
      <Center h="200px">
        <Spinner size="lg" />
      </Center>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        {fallback || (
          <Center h="200px">
            <Box textAlign="center">
              <Box fontSize="lg" mb={2}>
                Bạn cần đăng nhập để truy cập trang này
              </Box>
            </Box>
          </Center>
        )}
      </>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
