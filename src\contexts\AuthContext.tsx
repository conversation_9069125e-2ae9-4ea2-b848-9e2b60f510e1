import React, { createContext, useContext, useEffect, ReactNode, useState } from 'react';
import { useAuth, UseAuthReturn } from '../hooks/useAuthNew';
import { Spinner, Center, Box } from '@chakra-ui/react';

interface AuthContextType extends UseAuthReturn {
  uploadToken: string | null;
  setUploadToken: (token: string | null) => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const auth = useAuth();
  const [uploadToken, setUploadToken] = useState<string | null>(null);

  return (
    <AuthContext.Provider value={{ ...auth, uploadToken, setUploadToken }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};

// HOC for protected routes
interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback 
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <Center h="200px">
        <Spinner size="lg" />
      </Center>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        {fallback || (
          <Center h="200px">
            <Box textAlign="center">
              <Box fontSize="lg" mb={2}>
                Bạn cần đăng nhập để truy cập trang này
              </Box>
            </Box>
          </Center>
        )}
      </>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
