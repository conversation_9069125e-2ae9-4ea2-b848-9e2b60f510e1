import React from 'react';
import {
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  VStack,
  useColorModeValue
} from '@chakra-ui/react';

interface AuthErrorProps {
  error?: {
    message?: string;
  };
  onRetry: () => void;
  onLogout: () => void;
}

export const AuthError: React.FC<AuthErrorProps> = ({ error, onRetry, onLogout }) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Alert
      status="error"
      variant="subtle"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      textAlign="center"
      height="auto"
      p={8}
      borderRadius="lg"
      bg={bgColor}
      borderWidth="1px"
      borderColor="gray.200"
    >
      <AlertIcon boxSize="40px" mr={0} />
      <AlertTitle mt={4} mb={1} fontSize="lg">
        Lỗi xác thực
      </AlertTitle>
      <AlertDescription maxWidth="sm" mb={4}>
        {error?.message || 'Đã xảy ra lỗi khi xác thực. Vui lòng thử lại.'}
      </AlertDescription>
      <VStack spacing={3}>
        <Button
          colorScheme="blue"
          onClick={onRetry}
          size="md"
          width="full"
        >
          Thử lại
        </Button>
        <Button
          colorScheme="red"
          variant="outline"
          onClick={onLogout}
          size="md"
          width="full"
        >
          Đăng xuất
        </Button>
      </VStack>
    </Alert>
  );
};

export default AuthError; 