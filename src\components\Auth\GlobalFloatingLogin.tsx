import React from 'react';
import { useLocation } from 'react-router-dom';
import LoginButton from './LoginButton';
import { useAuth } from '../../hooks/useAuthNew';

interface GlobalFloatingLoginProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  hideOnPages?: string[];
}

const GlobalFloatingLogin: React.FC<GlobalFloatingLoginProps> = ({
  position = 'bottom-right',
  hideOnPages = []
}) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // Don't show if authenticated
  if (isAuthenticated) {
    return null;
  }

  // Don't show on specific pages
  if (hideOnPages.includes(location.pathname)) {
    return null;
  }

  // Don't show on login demo page to avoid conflicts
  if (location.pathname === '/login-demo') {
    return null;
  }

  return (
    <LoginButton 
      variant="google-style" 
      googleVariant="floating"
      position={position}
    />
  );
};

export default GlobalFloatingLogin;
