import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  Text,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  Box,
  Badge,
  Divider
} from '@chakra-ui/react';
import { FaGoogle, FaLock, FaUserPlus, FaSignInAlt } from 'react-icons/fa';
import { MdSecurity, MdVerifiedUser } from 'react-icons/md';
import { useAuth } from '../../hooks/useAuthNew';

interface LoginNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  feature?: string;
}

const LoginNotificationModal: React.FC<LoginNotificationModalProps> = ({
  isOpen,
  onClose,
  title = "Thông báo đăng nhập",
  message = "Bạn cần đăng nhập để sử dụng t<PERSON><PERSON> năng này",
  feature = "tính năng này"
}) => {
  const { login, isLoading } = useAuth();
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('blue.500', 'blue.300');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const handleGoogleLogin = async () => {
    setIsLoggingIn(true);
    try {
      await login();
      onClose(); // Close modal after successful login
    } catch (error) {
      console.error('Login failed:', error);
      // Error is handled by useAuth hook
    } finally {
      setIsLoggingIn(false);
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      isCentered
      size="md"
      closeOnOverlayClick={true}
    >
      <ModalOverlay 
        bg="blackAlpha.600" 
        backdropFilter="blur(4px)"
      />
      <ModalContent
        bg={bgColor}
        borderRadius="xl"
        border="1px solid"
        borderColor={borderColor}
        boxShadow="xl"
        mx={4}
      >
        <ModalHeader pb={2}>
          <HStack spacing={3}>
            <Box
              p={2}
              bg={accentColor}
              borderRadius="lg"
              color="white"
            >
              <Icon as={FaLock} boxSize="20px" />
            </Box>
            <VStack align="start" spacing={0}>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                {title}
              </Text>
              <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                Yêu cầu xác thực
              </Badge>
            </VStack>
          </HStack>
        </ModalHeader>
        
        <ModalCloseButton />
        
        <ModalBody py={4}>
          <VStack spacing={4} align="stretch">
            {/* Main Message */}
            <Box
              p={4}
              bg={useColorModeValue('blue.50', 'blue.900')}
              borderRadius="lg"
              border="1px solid"
              borderColor={useColorModeValue('blue.200', 'blue.700')}
            >
              <HStack spacing={3}>
                <Icon 
                  as={MdSecurity} 
                  color={accentColor} 
                  boxSize="24px" 
                />
                <VStack align="start" spacing={1}>
                  <Text fontSize="md" fontWeight="medium" color={textColor}>
                    {message}
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    Đăng nhập để truy cập {feature}
                  </Text>
                </VStack>
              </HStack>
            </Box>

            {/* Benefits */}
            <VStack spacing={2} align="stretch">
              <Text fontSize="sm" fontWeight="medium" color={textColor}>
                Lợi ích khi đăng nhập:
              </Text>
              
              <VStack spacing={2} align="stretch" pl={2}>
                <HStack spacing={3}>
                  <Icon as={MdVerifiedUser} color="green.500" boxSize="16px" />
                  <Text fontSize="sm" color={textColor}>
                    Truy cập đầy đủ tính năng admin
                  </Text>
                </HStack>
                
                <HStack spacing={3}>
                  <Icon as={FaUserPlus} color="purple.500" boxSize="16px" />
                  <Text fontSize="sm" color={textColor}>
                    Quản lý bài viết và nội dung
                  </Text>
                </HStack>
                
                <HStack spacing={3}>
                  <Icon as={FaSignInAlt} color="blue.500" boxSize="16px" />
                  <Text fontSize="sm" color={textColor}>
                    Đồng bộ dữ liệu trên nhiều thiết bị
                  </Text>
                </HStack>
              </VStack>
            </VStack>

            <Divider />

            {/* Security Note */}
            <Box
              p={3}
              bg={useColorModeValue('gray.50', 'gray.700')}
              borderRadius="md"
            >
              <Text fontSize="xs" color="gray.500" textAlign="center">
                🔒 Đăng nhập an toàn với Google OAuth 2.0
              </Text>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter pt={2}>
          <VStack spacing={3} w="100%">
            {/* Google Login Button */}
            <Button
              leftIcon={<FaGoogle />}
              onClick={handleGoogleLogin}
              isLoading={isLoggingIn || isLoading}
              loadingText="Đang đăng nhập..."
              w="100%"
              h="48px"
              bg="white"
              color="gray.700"
              border="2px solid"
              borderColor="gray.300"
              borderRadius="xl"
              fontSize="md"
              fontWeight="medium"
              _hover={{
                borderColor: accentColor,
                boxShadow: 'md',
                transform: 'translateY(-1px)'
              }}
              _active={{
                transform: 'translateY(0)'
              }}
              transition="all 0.2s"
            >
              Đăng nhập với Google
            </Button>

            {/* Cancel Button */}
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              color="gray.500"
              _hover={{
                color: textColor,
                bg: useColorModeValue('gray.100', 'gray.700')
              }}
            >
              Để sau
            </Button>
          </VStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default LoginNotificationModal;
