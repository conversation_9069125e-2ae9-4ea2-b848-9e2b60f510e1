import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>rovider, CSSReset, ColorModeScript } from '@chakra-ui/react';
import Nav from './components/Nav';
import ContentWithTransitions from './components/ContentWithTransitions';
import Footer from './components/Footer';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { AuthProvider } from './contexts/AuthContext';
import AuthErrorBoundary from './components/Auth/AuthErrorBoundary';
import LoginNotificationProvider from './components/Auth/LoginNotificationProvider';


import useUserStore from './store/useUserStore'; // Assuming useUserStore is now useUserStore.ts
import { blogConfig } from './config'; // Assuming config is now config.ts
import theme from './theme/cobaltTheme';
import ThemeProvider from './components/ThemeP<PERSON>ider';
import { initializeDatabase } from './utils/indexedDBUtils';
import './utils/tokenTest'; // Import token test utility
import './utils/dbTest'; // Import database test utility

function App() {
  const initializeUser = useUserStore(state => state.initializeUser);
  const [authRefreshKey, setAuthRefreshKey] = useState(0);

  useEffect(() => {
    // Initialize database first, then user store
    const init = async () => {
      try {
        console.log('🚀 Starting app initialization...');

        // Initialize database first
        const dbInitialized = await initializeDatabase();
        if (!dbInitialized) {
          console.error('❌ Database initialization failed, but continuing...');
        }

        // Then initialize user store
        await initializeUser();

        console.log('✅ App initialization completed');
      } catch (error) {
        console.error('❌ App initialization error:', error);
        // Continue anyway to avoid blocking the app
      }
    };

    init();
  }, [initializeUser]);

  // Global auth state change listener (DISABLED to prevent infinite loops)
  // useEffect(() => {
  //   const handleAuthStateChange = (event: CustomEvent) => {
  //     console.log('[App] Global auth state changed:', event.detail);
  //     setAuthRefreshKey(prev => prev + 1);

  //     // Force re-render of all components by triggering a global refresh
  //     setTimeout(() => {
  //       window.dispatchEvent(new CustomEvent('globalRefresh'));
  //     }, 100);
  //   };

  //   window.addEventListener('authStateChanged', handleAuthStateChange as EventListener);

  //   return () => {
  //     window.removeEventListener('authStateChanged', handleAuthStateChange as EventListener);
  //   };
  // }, []);

  // Ensure clientId is defined before rendering GoogleOAuthProvider
  if (!blogConfig.clientId) {
    console.error('Google OAuth Client ID is not defined in blogConfig.');
    // Render a fallback UI or an error message if clientId is missing
    return <div>Error: Google OAuth Client ID is not configured.</div>;
  }

  return (
    <>
      <ColorModeScript initialColorMode={theme.config.initialColorMode} />
      <GoogleOAuthProvider clientId={blogConfig.clientId}>
        <ChakraProvider theme={theme}>
          <CSSReset />
          <ThemeProvider>
            <Router>
              <AuthErrorBoundary>
                <AuthProvider>
                  <LoginNotificationProvider>
                    <Nav />
                    <ContentWithTransitions />
                    <Footer />
                  </LoginNotificationProvider>
                </AuthProvider>
              </AuthErrorBoundary>
            </Router>
          </ThemeProvider>
        </ChakraProvider>
      </GoogleOAuthProvider>
    </>
  );
}

export default App;