// Database test utility to verify IndexedDB functionality
import { 
  initializeDatabase, 
  debugDatabase, 
  verifyAndRepairDatabase,
  getHistoryData,
  saveHistoryData,
  ensureStoreExists
} from './indexedDBUtils';

// Test database functionality
export const testDatabase = async (): Promise<void> => {
  console.log('🧪 Starting database tests...');
  
  try {
    // Test 1: Initialize database
    console.log('\n1. Testing database initialization...');
    const initialized = await initializeDatabase();
    console.log('Database initialization result:', initialized);
    
    // Test 2: Debug database state
    console.log('\n2. Debugging database state...');
    await debugDatabase();
    
    // Test 3: Verify integrity
    console.log('\n3. Verifying database integrity...');
    const verified = await verifyAndRepairDatabase();
    console.log('Database verification result:', verified);
    
    // Test 4: Test follows store specifically
    console.log('\n4. Testing follows store...');
    const followsExists = await ensureStoreExists('follows');
    console.log('Follows store exists:', followsExists);
    
    // Test 5: Test reading/writing to follows store
    console.log('\n5. Testing follows store operations...');
    try {
      const testData = await getHistoryData('follows', 'test-user');
      console.log('Read test data from follows store:', testData);
      
      await saveHistoryData('follows', 'test-user', { id: 'test-post', title: 'Test Post' });
      console.log('Successfully saved test data to follows store');
      
      const updatedData = await getHistoryData('follows', 'test-user');
      console.log('Updated test data from follows store:', updatedData);
    } catch (storeError) {
      console.error('Error testing follows store operations:', storeError);
    }
    
    console.log('\n✅ Database tests completed');
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
};

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Run tests after a short delay to ensure DOM is ready
  setTimeout(() => {
    testDatabase();
  }, 2000);
}
