// Database test utility to verify IndexedDB functionality
import {
  initializeDatabase,
  getHistoryData,
  saveHistoryData
} from './indexedDBUtils';

// Test database functionality
export const testDatabase = async (): Promise<void> => {
  console.log('🧪 Starting database tests...');

  try {
    // Test 1: Initialize database
    console.log('\n1. Testing database initialization...');
    const initialized = await initializeDatabase();
    console.log('Database initialization result:', initialized);

    // Test 2: Test reading/writing to favorites store
    console.log('\n2. Testing favorites store operations...');
    try {
      const testData = await getHistoryData('favorites', 'test-user');
      console.log('Read test data from favorites store:', testData);

      await saveHistoryData('favorites', 'test-user', [{ id: 'test-post', title: 'Test Post' }]);
      console.log('Successfully saved test data to favorites store');

      const updatedData = await getHistoryData('favorites', 'test-user');
      console.log('Updated test data from favorites store:', updatedData);
    } catch (storeError) {
      console.error('Error testing favorites store operations:', storeError);
    }

    // Test 3: Test bookmarks store
    console.log('\n3. Testing bookmarks store operations...');
    try {
      const bookmarkData = await getHistoryData('bookmarks', 'test-user');
      console.log('Read bookmark data:', bookmarkData);

      await saveHistoryData('bookmarks', 'test-user', [{ id: 'test-manga', title: 'Test Manga' }]);
      console.log('Successfully saved bookmark data');
    } catch (storeError) {
      console.error('Error testing bookmarks store operations:', storeError);
    }

    console.log('\n✅ Database tests completed');
  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
};

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Run tests after a short delay to ensure DOM is ready
  setTimeout(() => {
    testDatabase();
  }, 2000);
}
