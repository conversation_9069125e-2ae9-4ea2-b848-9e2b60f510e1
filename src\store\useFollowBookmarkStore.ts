import { create } from 'zustand';
import { getHistoryData, saveHistoryData } from '../utils/indexedDBUtils';
import { backupUserData } from '../api/auth';
import { MAX_BOOKMARKS, FollowedPost, MangaBookmark } from '../utils/postUtils';
import { extractImage, Post } from '../utils/blogUtils';

// Interfaces
interface ToastFunction {
  (options: {
    title: string;
    description: string;
    status: 'success' | 'error' | 'warning' | 'info';
    duration: number;
    isClosable: boolean;
  }): void;
}

interface SyncResult {
  followCount: number;
  bookmarkCount: number;
  synced: boolean;
  changes: {
    follows: number;
    bookmarks: number;
  };
}

interface FollowBookmarkStore {
  follows: any[];
  bookmarks: any[];
  loading: boolean;
  error: string | null;
  initialize: (userId: string) => Promise<void>;
  toggleFollow: (post: Post, userId: string, accessToken: string | null, toast?: ToastFunction) => Promise<boolean>;
  isFollowed: (postId: string) => boolean;
  toggleBookmark: (mangaData: MangaBookmark, userId: string, accessToken: string | null, toast?: ToastFunction) => Promise<boolean>;
  isBookmarked: (mangaId: string) => boolean;
  getBookmarkData: (mangaId: string) => MangaBookmark | undefined;
  syncGuestData: (userId: string, toast?: ToastFunction) => Promise<SyncResult | false>;
}

const useFollowBookmarkStore = create<FollowBookmarkStore>((set, get) => ({
  // State
  follows: [],
  bookmarks: [],
  loading: false,
  error: null,

  // Actions
  initialize: async (userId: string): Promise<void> => {
    if (!userId) return;

    set({ loading: true, error: null });
    try {
      const [follows, bookmarks] = await Promise.all([
        getHistoryData('favorites', userId),
        getHistoryData('bookmarks', userId)
      ]);

      set({
        follows: Array.isArray(follows) ? follows : [],
        bookmarks: Array.isArray(bookmarks) ? bookmarks : [],
        loading: false
      });
    } catch (error: any) {
      console.error('Error initializing follow/bookmark store:', error);
      set({ error: 'Failed to load data', loading: false });
    }
  },

  // Follow actions
  toggleFollow: async (post: Post, userId: string, accessToken: string | null, toast?: ToastFunction): Promise<boolean> => {
    if (!userId || userId === 'guest') {
      toast?.({
        title: 'Cần đăng nhập',
        description: 'Vui lòng đăng nhập để sử dụng tính năng favorite',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }

    const { follows } = get();
    const isCurrentlyFollowed = follows.some(item => item.id === post.id);

    try {
      let updatedFollows: any[];
      if (isCurrentlyFollowed) {
        updatedFollows = follows.filter(item => item.id !== post.id);
      } else {
        const newFollow: FollowedPost = {
          id: post.id!,
          title: post.title,
          url: post.url,
          published: post.published,
          updated: post.updated,
          labels: post.labels,
          thumbnail: post.thumbnail || (post.content ? extractImage(post.content) : null),
          followAt: Date.now(),
        };
        updatedFollows = [newFollow, ...follows].slice(0, 1000);
      }

      userId ? await saveHistoryData('favorites', userId, updatedFollows) : Promise.resolve();
      set({ follows: updatedFollows });

      // Backup to Google Drive if logged in
      if (accessToken) {
        try {
          const backupData = {
            favoritePosts: updatedFollows,
            mangaBookmarks: get().bookmarks,
            readPosts: userId ? await getHistoryData('reads', userId) : []
          };
          await backupUserData(accessToken, userId, backupData);
          console.log('Backup to Google Drive successful');
        } catch (error: any) {
          console.error('Error backing up to Google Drive:', error);
          toast?.({
            title: 'Lỗi sao lưu',
            description: 'Không thể sao lưu dữ liệu lên Google Drive',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }

      toast?.({
        title: isCurrentlyFollowed ? 'Đã bỏ yêu thích' : 'Đã yêu thích',
        description: isCurrentlyFollowed ? 'Bạn đã bỏ yêu thích truyện này' : 'Bạn đã yêu thích truyện này',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      return !isCurrentlyFollowed;
    } catch (error: any) {
      console.error('Error toggling follow:', error);
      toast?.({
        title: 'Lỗi',
        description: 'Không thể thực hiện thao tác yêu thích',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }
  },

  isFollowed: (postId: string): boolean => {
    return get().follows.some(item => item.id === postId);
  },

  // Bookmark actions
  toggleBookmark: async (mangaData: MangaBookmark, userId: string, accessToken: string | null, toast?: ToastFunction): Promise<boolean> => {
    if (!userId || userId === 'guest') {
      toast?.({
        title: 'Cần đăng nhập',
        description: 'Vui lòng đăng nhập để sử dụng tính năng bookmark',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }

    const { bookmarks } = get();
    const existingBookmark = bookmarks.find(b => b.id === mangaData.id);

    try {
      let updatedBookmarks: any[];
      if (existingBookmark) {
        updatedBookmarks = bookmarks.filter(b => b.id !== mangaData.id);
      } else {
        const newBookmark: MangaBookmark = {
          id: mangaData.id,
          title: mangaData.title,
          url: mangaData.url,
          currentPage: mangaData.currentPage,
          totalPages: (mangaData as any).totalPages,
          verticalMode: (mangaData as any).verticalMode,
          timestamp: Date.now(),
          bookmarkAt: Date.now()
        };
        updatedBookmarks = [newBookmark, ...bookmarks].slice(0, MAX_BOOKMARKS);
      }

      userId ? await saveHistoryData('bookmarks', userId, updatedBookmarks) : Promise.resolve();
      set({ bookmarks: updatedBookmarks });

      // Backup to Google Drive if logged in
      if (accessToken) {
        try {
          const backupData = {
            favoritePosts: get().follows,
            mangaBookmarks: updatedBookmarks,
            readPosts: userId ? await getHistoryData('reads', userId) : []
          };
          await backupUserData(accessToken, userId, backupData);
        } catch (error: any) {
          console.error('Error backing up to Google Drive:', error);
        }
      }

      toast?.({
        title: existingBookmark ? 'Đã bỏ bookmark' : 'Đã bookmark',
        description: existingBookmark ? 'Bạn đã bỏ bookmark truyện này' : 'Bạn đã bookmark truyện này',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      return !existingBookmark;
    } catch (error: any) {
      console.error('Error toggling bookmark:', error);
      toast?.({
        title: 'Lỗi',
        description: 'Không thể thực hiện thao tác bookmark',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }
  },

  isBookmarked: (mangaId: string): boolean => {
    return get().bookmarks.some(item => item.id === mangaId);
  },

  getBookmarkData: (mangaId: string): MangaBookmark | undefined => {
    return get().bookmarks.find(item => item.id === mangaId);
  },

  // Sync actions
  syncGuestData: async (userId: string, toast?: ToastFunction): Promise<SyncResult | false> => {
    if (!userId) return false;

    try {
      const [guestFollows, guestBookmarks, userFollows, userBookmarks] = await Promise.all([
        getHistoryData('favorites', 'guest'),
        getHistoryData('bookmarks', 'guest'),
        getHistoryData('favorites', userId),
        getHistoryData('bookmarks', userId)
      ]);

      const mergeArrays = <T extends { id: string; [key: string]: any }>(
        user: T[] | any,
        guest: T[] | any,
        timestampKey: string
      ): T[] => {
        const userArray = Array.isArray(user) ? user : [];
        const guestArray = Array.isArray(guest) ? guest : [];
        const merged = [...userArray, ...guestArray];
        const unique = Array.from(new Map(merged.map((item: any) => [item.id, item])).values());
        return unique.sort((a: any, b: any) => (b[timestampKey] || 0) - (a[timestampKey] || 0));
      };

      const mergedFollows = mergeArrays<FollowedPost>(userFollows, guestFollows, 'followAt');
      const mergedBookmarks = mergeArrays<MangaBookmark>(userBookmarks, guestBookmarks, 'timestamp');

      await Promise.all([
        saveHistoryData('favorites', userId, mergedFollows),
        saveHistoryData('bookmarks', userId, mergedBookmarks),
        saveHistoryData('favorites', 'guest', []),
        saveHistoryData('bookmarks', 'guest', [])
      ]);

      set({
        follows: mergedFollows,
        bookmarks: mergedBookmarks
      });

      return {
        followCount: mergedFollows.length,
        bookmarkCount: mergedBookmarks.length,
        synced: true,
        changes: {
          follows: mergedFollows.length - (Array.isArray(userFollows) ? userFollows.length : 0),
          bookmarks: mergedBookmarks.length - (Array.isArray(userBookmarks) ? userBookmarks.length : 0)
        }
      };
    } catch (error: any) {
      console.error('Error syncing guest data:', error);
      toast?.({
        title: 'Lỗi',
        description: 'Không thể đồng bộ dữ liệu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return false;
    }
  }
}));

export default useFollowBookmarkStore;