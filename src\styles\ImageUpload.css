.upload-form {
  border: 2px dashed #ccc;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.preview-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  margin-top: 20px;
}

.preview-item {
  position: relative;
  border: 1px solid #ddd;
  padding: 5px;
  border-radius: 4px;
}

.preview-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.preview-item .remove-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: red;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  margin-top: 10px;
  overflow: hidden;
}

.progress {
  width: 0%;
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
} 