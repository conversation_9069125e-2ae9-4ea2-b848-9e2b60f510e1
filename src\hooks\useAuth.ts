import { useState, useEffect, useCallback } from 'react';
import { useNavigate, NavigateFunction } from 'react-router-dom';
import {
  getStoredToken,
  getStoredRefreshToken,
  isTokenValid,
  refreshToken,
  getUserInfo,
  handleLogin,
  handleLogout
} from '../api/auth';
import { handleError, AppError } from '../api';
import useUserStore from '../store/useUserStore';
import { saveUserData } from '../utils/indexedDBUtils';

// Interfaces
interface User {
  sub: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email?: string;
  email_verified?: boolean;
}

interface UserInfo extends User {
  accessToken?: string;
}

interface ToastProps {
  title: string;
  description: string;
  status: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  isClosable: boolean;
}

interface AuthResponse {
  code?: string;
  access_token?: string;
  [key: string]: any;
}

interface UseAuthReturn {
  user: User | null;
  loading: boolean;
  error: AppError | null;
  isAuthenticated: boolean;
  login: (response: AuthResponse) => Promise<void>;
  logout: () => Promise<void>;
  retry: () => void;
}

export const useAuth = (): UseAuthReturn => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<AppError | null>(null);
  const navigate: NavigateFunction = useNavigate();
  const { userId, accessToken, setUser, setGuestMode } = useUserStore();

  const checkAuth = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      const token = await getStoredToken();
      if (!token) {
        setLoading(false);
        return;
      }

      // Kiểm tra token hiện tại
      let isValid = await isTokenValid(token);
      let currentToken = token;

      // Nếu token không hợp lệ, thử refresh
      if (!isValid) {
        const refreshTokenValue = await getStoredRefreshToken();
        if (refreshTokenValue) {
          try {
            currentToken = await refreshToken(refreshTokenValue);
            isValid = true;
          } catch (refreshError) {
            console.error('Failed to refresh token:', refreshError);
            return;
          }
        } else {
          setLoading(false);
          return;
        }
      }

      // Lấy thông tin user
      const userInfo = await getUserInfo(currentToken);
      if (!userInfo.sub) {
        throw new Error('Invalid user info');
      }

      // Lưu user info vào IndexedDB
      const userDataToSave: UserInfo = {
        sub: userInfo.sub,
        name: userInfo.name,
        given_name: userInfo.given_name,
        family_name: userInfo.family_name,
        picture: userInfo.picture,
        email: userInfo.email,
        email_verified: userInfo.email_verified,
        accessToken: currentToken
      };
      await saveUserData(userInfo.sub, userDataToSave);

      setUser(userInfo.sub, currentToken);
    } catch (err: any) {
      const handledError = handleError(err);
      setError(handledError);
    } finally {
      setLoading(false);
    }
  }, [setUser]);

  const login = useCallback(async (response: AuthResponse): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      await handleLogin({
        response,
        setUser,
        setGuestMode,
        initializeUser: checkAuth,
        navigate,
        toast: (props: ToastProps) => console.log('Toast:', props), // Implement toast if needed
        onClose: () => {}
      });
    } catch (err: any) {
      const handledError = handleError(err);
      setError(handledError);
      setGuestMode();
    } finally {
      setLoading(false);
    }
  }, [navigate, setUser, setGuestMode, checkAuth]);

  const logout = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      await handleLogout({
        userId,
        navigate,
        toast: (props: ToastProps) => console.log('Toast:', props), // Implement toast if needed
        onClose: () => {}
      });
    } catch (err: any) {
      const handledError = handleError(err);
      setError(handledError);
    } finally {
      setLoading(false);
    }
  }, [navigate, userId]);

  const retry = useCallback((): void => {
    setError(null);
    checkAuth();
  }, [checkAuth]);

  // Kiểm tra auth khi component mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Tự động refresh token trước khi hết hạn
  useEffect(() => {
    if (!accessToken) return;

    const checkTokenInterval = setInterval(async () => {
      try {
        const token = await getStoredToken();
        if (!token) return;

        const isValid = await isTokenValid(token);
        if (!isValid) {
          const refreshTokenValue = await getStoredRefreshToken();
          if (refreshTokenValue) {
            const newToken = await refreshToken(refreshTokenValue);
            setUser(userId, newToken);
          } else {
            console.warn('Refresh token not found');
          }
        }
      } catch (error: any) {
        console.error('Token refresh error:', error);
      }
    }, 5 * 60 * 1000); // Kiểm tra mỗi 5 phút

    return () => clearInterval(checkTokenInterval);
  }, [accessToken, userId, setUser]);

  return {
    user: userId !== 'guest' ? { sub: userId } : null,
    loading,
    error,
    isAuthenticated: userId !== 'guest',
    login,
    logout,
    retry
  };
};