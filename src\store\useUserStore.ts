import { create } from 'zustand';
import { persist, StorageValue } from 'zustand/middleware';
import {
  encryptAndStoreToken,
  getAndDecryptToken,
  encryptAndStoreUserData,
  getAndDecryptUserData,
  clearEncryptedData,
  createSecureSession,
  validateSession,
  generateTOTP,
  verifyTOTP,
  decryptData
} from '../utils/securityUtils';
import { clearUserInfo, setUserInfo } from '../utils/userUtils';
import {
  clearHistoryData,
  getHistoryData,
  saveHistoryData,
  deleteUserData,
  getUserData,
  saveUserData,
  getDataFromDB
} from '../utils/indexedDBUtils';
import { clearCachedData, CACHE_KEYS } from '../utils/cache';
import { backupUserData, isTokenValid, refreshToken, getStoredRefreshToken } from '../api/auth';

export interface User {
  sub: string; // Google User ID
  name: string;
  given_name?: string;
  family_name?: string;
  picture: string;
  email: string;
  email_verified: boolean;
  locale?: string;
  is2FAEnabled?: boolean;
  twoFactorSecret?: string | null;
  isAuthenticated?: boolean;
  id?: string;
  updatedAt?: number;
}

interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  accessToken: string | null;
  is2FAEnabled: boolean;
  is2FAVerified: boolean;
  lastSyncTime: number | null;
  syncStatus: 'idle' | 'syncing' | 'error' | 'success';
  syncError: string | null;
  isOffline: boolean;
  userId: string | null;
  storeReady: boolean;
  temp2FASecret?: string | null;
  temp2FACode?: string | null;
  hasAccessToken: boolean;
  hasUserId: boolean;
}

interface UserActions {
  initializeUser: () => Promise<boolean>;
  setUser: (userData: User | null, accessToken: string | null) => Promise<boolean>;
  setAccessToken: (token: string | null) => Promise<boolean>;
  getAccessToken: () => Promise<string | null>;
  enable2FA: () => Promise<{ secret: string | null, code: string | null } | null>;
  verifyAndEnable2FA: (code: string) => Promise<boolean>;
  verify2FA: (code: string) => Promise<boolean>;
  disable2FA: (code: string) => Promise<boolean>;
  syncUserData: () => Promise<boolean>;
  setOfflineStatus: (isOffline: boolean) => void;
  updateProfile: (updates: Partial<User>) => Promise<boolean>;
  clearUserData: () => Promise<void>;
  checkAuthStatus: () => Promise<boolean>;
  getValidAccessToken: () => Promise<string | null>;
  logout: () => Promise<void>;
}

interface UserStore extends UserState, UserActions {}

interface StorageData {
  state: UserState;
  version: number;
  timestamp: number;
}

const STORE_KEY = 'user-storage';

const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      accessToken: null,
      is2FAEnabled: false,
      is2FAVerified: false,
      lastSyncTime: null,
      syncStatus: 'idle',
      syncError: null,
      isOffline: false,
      userId: null,
      storeReady: false,
      temp2FASecret: null,
      temp2FACode: null,
      hasAccessToken: false,
      hasUserId: false,

      initializeUser: async () => {
        try {
          console.log('[useUserStore] Initializing user...');

          // Get current state before initialization
          const currentState = get();
          console.log('[useUserStore] Current state before init:', {
            hasUser: !!currentState.user,
            isAuthenticated: currentState.isAuthenticated,
            hasAccessToken: currentState.hasAccessToken,
            accessTokenLength: currentState.accessToken ? currentState.accessToken.length : 0
          });

          // Load user data
          const userData = await getAndDecryptUserData(); // This returns UserData | null
          console.log('[useUserStore] User data loaded:', !!userData);

          // Load access token
          const accessToken = await getAndDecryptToken();
          console.log('[useUserStore] Access token loaded:', {
            hasToken: !!accessToken,
            tokenLength: accessToken ? accessToken.length : 0,
            tokenPreview: accessToken ? `${accessToken.substring(0, 10)}...` : 'null'
          });

          if (userData) {
            // Ensure userData is cast to User for consistency, handling missing fields
            const user: User = {
              sub: userData.sub || '', // sub is string in User, might be undefined in UserData
              name: userData.name || '',
              given_name: userData.given_name,
              family_name: userData.family_name,
              picture: userData.picture || '',
              email: userData.email || '',
              email_verified: userData.email_verified || false,
              locale: userData.locale,
              is2FAEnabled: userData.is2FAEnabled,
              twoFactorSecret: userData.twoFactorSecret,
              isAuthenticated: userData.isAuthenticated,
              id: userData.id,
              updatedAt: userData.updatedAt,
            };

            const newState = {
              user: user,
              isAuthenticated: true,
              accessToken: accessToken || null,
              storeReady: true,
              hasUserId: !!(user.sub || user.id),
              userId: user.sub || user.id || null, // Ensure userId is string | null
              hasAccessToken: !!accessToken
            };

            console.log('[useUserStore] Setting state in initializeUser:', {
              hasUser: !!newState.user,
              isAuthenticated: newState.isAuthenticated,
              hasAccessToken: newState.hasAccessToken,
              accessTokenLength: newState.accessToken ? newState.accessToken.length : 0,
              userId: newState.userId
            });

            set(newState);
            console.log('[useUserStore] User initialized successfully');
            return true;
          }

          set({
            storeReady: true,
            hasUserId: false,
            isAuthenticated: false,
            user: null,
            userId: null,
            accessToken: null,
            hasAccessToken: false
          });
          console.log('[useUserStore] No user data found');
          return false;
        } catch (error: any) {
          console.error('[useUserStore] Error initializing user:', error);
          set({
            storeReady: true,
            hasUserId: false,
            isAuthenticated: false,
            user: null,
            userId: null,
            accessToken: null,
            hasAccessToken: false
          });
          return false;
        }
      },

      setUser: async (userData: User | null, accessToken: string | null) => {
        try {
          console.log('[useUserStore] setUser called with:', {
            hasUserData: !!userData,
            hasAccessToken: !!accessToken,
            accessTokenLength: accessToken ? accessToken.length : 0,
            userId: userData ? (userData.sub || userData.id) : null
          });

          if (userData) {
            // Save user data
            console.log('[useUserStore] Saving user data to IndexedDB...');
            const userIdToSave = userData.sub || userData.id; // Ensure it's string or undefined/null
            if (userIdToSave) {
              await saveUserData(userIdToSave, userData);
            } else {
              console.warn('[useUserStore] Cannot save user data: userId is missing.', userData);
            }

            // Save token if provided
            if (accessToken) {
              console.log('[useUserStore] Encrypting and storing access token...');
              await encryptAndStoreToken(accessToken);
            }

            set({
              user: userData, // Ensure this is User | null
              isAuthenticated: true,
              accessToken: accessToken || null,
              hasUserId: !!(userData.sub || userData.id),
              userId: userData.sub || userData.id || null,
              hasAccessToken: !!accessToken
            });
            return true;
          }

          // If userData is null, clear everything
          console.log('[useUserStore] setUser called with null userData, clearing...');
          await clearEncryptedData();
          await clearHistoryData();
          await deleteUserData(); // Also clear IndexedDB user data
          clearUserInfo();
          clearCachedData(CACHE_KEYS.ATOM_POSTS);

          set({
            user: null,
            isAuthenticated: false,
            accessToken: null,
            hasUserId: false,
            userId: null,
            hasAccessToken: false,
            is2FAEnabled: false,
            is2FAVerified: false,
            lastSyncTime: null,
            syncStatus: 'idle',
            syncError: null,
            isOffline: false,
            temp2FASecret: null,
            temp2FACode: null,
          });
          return false;

        } catch (error: any) {
          console.error('[useUserStore] Error setting user:', error);
          return false;
        }
      },

      setAccessToken: async (token) => {
        try {
          if (token) {
            await encryptAndStoreToken(token);
          } else {
            await clearEncryptedData();
          }
          set({ accessToken: token, hasAccessToken: !!token });
          return true;
        } catch (error) {
          console.error('[useUserStore] Error setting access token:', error);
          return false;
        }
      },

      getAccessToken: async () => {
        try {
          return await getAndDecryptToken();
        } catch (error) {
          console.error('[useUserStore] Error getting access token:', error);
          return null;
        }
      },

      // 2FA functions
      enable2FA: async () => {
        try {
          if (!get().user?.email) throw new Error('User email not available');
          const secret = generateTOTP();
          const code = verifyTOTP(secret);
          set({
            temp2FASecret: secret,
            temp2FACode: code,
          });
          return { secret, code };
        } catch (error) {
          console.error('Error enabling 2FA:', error);
          return null;
        }
      },

      verifyAndEnable2FA: async (code) => {
        try {
          const tempSecret = get().temp2FASecret;
          if (!tempSecret) throw new Error('2FA secret not found');

          const isValid = verifyTOTP(tempSecret, code);
          if (isValid) {
            await saveUserData(get().userId!, { ...get().user!, is2FAEnabled: true, twoFactorSecret: tempSecret });
            set({
              is2FAEnabled: true,
              twoFactorSecret: tempSecret,
              is2FAVerified: true,
              temp2FASecret: null,
              temp2FACode: null,
            });
            return true;
          } else {
            console.warn('Invalid 2FA code during enable');
            return false;
          }
        } catch (error) {
          console.error('Error verifying and enabling 2FA:', error);
          return false;
        }
      },

      verify2FA: async (code) => {
        try {
          const secret = get().user?.twoFactorSecret;
          if (!secret) throw new Error('2FA not enabled');

          const isValid = verifyTOTP(secret, code);
          if (isValid) {
            set({ is2FAVerified: true });
            return true;
          } else {
            console.warn('Invalid 2FA code');
            set({ is2FAVerified: false });
            return false;
          }
        } catch (error) {
          console.error('Error verifying 2FA:', error);
          set({ is2FAVerified: false });
          return false;
        }
      },

      disable2FA: async (code) => {
        try {
          const secret = get().user?.twoFactorSecret;
          if (!secret) throw new Error('2FA not enabled');

          const isValid = verifyTOTP(secret, code);
          if (isValid) {
            await saveUserData(get().userId!, { ...get().user!, is2FAEnabled: false, twoFactorSecret: null });
            set({
              is2FAEnabled: false,
              twoFactorSecret: null,
              is2FAVerified: false,
            });
            return true;
          } else {
            console.warn('Invalid 2FA code during disable');
            return false;
          }
        } catch (error) {
          console.error('Error disabling 2FA:', error);
          return false;
        }
      },

      // Sync user data to cloud (e.g., Firebase, backend)
      syncUserData: async () => {
        set({ syncStatus: 'syncing', syncError: null });
        try {
          const user = get().user;
          if (!user) throw new Error('User not authenticated for sync');

          // Simulate API call to backup data
          await backupUserData(user);

          set({ lastSyncTime: Date.now(), syncStatus: 'success' });
          return true;
        } catch (error: any) {
          console.error('Sync user data error:', error);
          set({ syncStatus: 'error', syncError: error.message || 'Failed to sync data' });
          return false;
        }
      },

      setOfflineStatus: (isOffline) => {
        set({ isOffline });
      },

      updateProfile: async (updates) => {
        try {
          const currentUser = get().user;
          if (!currentUser) throw new Error('User not authenticated');

          const updatedUser = { ...currentUser, ...updates };
          await saveUserData(updatedUser.sub || updatedUser.id, updatedUser);

          set({ user: updatedUser });
          return true;
        } catch (error) {
          console.error('Error updating profile:', error);
          return false;
        }
      },

      clearUserData: async () => {
        try {
          console.log('[useUserStore] Clearing all user data...');
          await clearEncryptedData();
          await clearHistoryData();
          await deleteUserData(); // Clears user data from IndexedDB
          clearUserInfo(); // Clears user info from localStorage (if any)
          clearCachedData(CACHE_KEYS.ATOM_POSTS); // Clear specific cached data

          set({
            user: null,
            isAuthenticated: false,
            accessToken: null,
            is2FAEnabled: false,
            is2FAVerified: false,
            lastSyncTime: null,
            syncStatus: 'idle',
            syncError: null,
            isOffline: false,
            userId: null,
            storeReady: true,
            temp2FASecret: null,
            temp2FACode: null,
            hasAccessToken: false,
            hasUserId: false,
          });
          console.log('[useUserStore] All user data cleared successfully.');
        } catch (error) {
          console.error('[useUserStore] Error clearing user data:', error);
        }
      },

      checkAuthStatus: async () => {
        try {
          const token = await get().getValidAccessToken();
          const user = get().user;
          return !!token && !!user; // Simple check
        } catch (error) {
          console.error('[useUserStore] Error checking auth status:', error);
          return false;
        }
      },

      getValidAccessToken: async () => {
        try {
          const storedAccessToken = await getAndDecryptToken();
          if (!storedAccessToken) {
            console.log('[useUserStore] No stored access token found.');
            return null;
          }

          if (isTokenValid(storedAccessToken)) {
            console.log('[useUserStore] Stored access token is valid.');
            return storedAccessToken;
          }

          console.log('[useUserStore] Stored access token is expired, attempting refresh...');
          const newAccessToken = await refreshToken();
          if (newAccessToken) {
            await encryptAndStoreToken(newAccessToken);
            set({ accessToken: newAccessToken, hasAccessToken: true });
            console.log('[useUserStore] Token refreshed and stored.');
            return newAccessToken;
          } else {
            console.warn('[useUserStore] Could not refresh token. User needs to re-authenticate.');
            await get().logout(); // Logout if refresh fails
            return null;
          }
        } catch (error) {
          console.error('[useUserStore] Error getting valid access token:', error);
          await get().logout(); // Logout on any token fetching/refreshing error
          return null;
        }
      },

      logout: async () => {
        try {
          console.log('[useUserStore] Logout from store...');
          // Clear authentication tokens and user data
          await clearEncryptedData();
          await clearHistoryData();
          await deleteUserData(); // Clear IndexedDB user data
          clearUserInfo(); // Clear localStorage user info
          clearCachedData(CACHE_KEYS.ATOM_POSTS);

          set({
            user: null,
            isAuthenticated: false,
            accessToken: null,
            is2FAEnabled: false,
            is2FAVerified: false,
            lastSyncTime: null,
            syncStatus: 'idle',
            syncError: null,
            isOffline: false,
            userId: null,
            storeReady: true,
            temp2FASecret: null,
            temp2FACode: null,
            hasAccessToken: false,
            hasUserId: false,
          });
          console.log('[useUserStore] User logged out and data cleared.');
        } catch (error) {
          console.error('[useUserStore] Error during store logout:', error);
        }
      },

    }),
    {
      name: STORE_KEY,
      getStorage: () => localStorage,
      // Custom serialize/deserialize for better security and type safety
      serialize: (data: StorageValue<UserStore>) => {
        // Only persist non-sensitive data, or encrypt if sensitive
        const userToPersist = data.state.user;
        const serializedUser = userToPersist ? {
            sub: userToPersist.sub || '',
            name: userToPersist.name || '',
            given_name: userToPersist.given_name,
            family_name: userToPersist.family_name,
            picture: userToPersist.picture || '',
            email: userToPersist.email || '',
            email_verified: userToPersist.email_verified || false,
            locale: userToPersist.locale,
            id: userToPersist.id,
            updatedAt: userToPersist.updatedAt,
          } : null;

        const stateToPersist = {
          user: serializedUser,
          isAuthenticated: data.state.isAuthenticated,
          is2FAEnabled: data.state.is2FAEnabled,
          is2FAVerified: data.state.is2FAVerified,
          lastSyncTime: data.state.lastSyncTime,
          syncStatus: data.state.syncStatus,
          syncError: data.state.syncError,
          isOffline: data.state.isOffline,
          userId: data.state.userId,
          storeReady: data.state.storeReady,
          hasUserId: data.state.hasUserId,
        };
        return JSON.stringify({ state: stateToPersist, version: data.version, timestamp: Date.now() });
      },
      deserialize: (str: string) => {
        const data = JSON.parse(str);
        const state = data.state;
        const parsedUser = state.user;
        const user: User | null = parsedUser && typeof parsedUser === 'object' ? {
          sub: parsedUser.sub || '',
          name: parsedUser.name || '',
          given_name: parsedUser.given_name,
          family_name: parsedUser.family_name,
          picture: parsedUser.picture || '',
          email: parsedUser.email || '',
          email_verified: parsedUser.email_verified || false,
          locale: parsedUser.locale,
          is2FAEnabled: parsedUser.is2FAEnabled,
          twoFactorSecret: parsedUser.twoFactorSecret,
          isAuthenticated: parsedUser.isAuthenticated,
          id: parsedUser.id,
          updatedAt: parsedUser.updatedAt,
        } : null;

        return {
          state: { 
            ...state,
            user, // Use the re-constructed user object
            // accessToken, temp2FASecret, temp2FACode are not persisted for security
            accessToken: null,
            temp2FASecret: null,
            temp2FACode: null,
            // Ensure all properties are set, even if null from storage
            isAuthenticated: state.isAuthenticated || false,
            is2FAEnabled: state.is2FAEnabled || false,
            is2FAVerified: state.is2FAVerified || false,
            lastSyncTime: state.lastSyncTime || null,
            syncStatus: state.syncStatus || 'idle',
            syncError: state.syncError || null,
            isOffline: state.isOffline || false,
            userId: state.userId || null,
            storeReady: true,
            hasAccessToken: false, // Access token is not directly persisted
            hasUserId: state.hasUserId || false,
          },
          version: data.version,
          timestamp: data.timestamp,
        } as StorageValue<UserStore>; // Cast to StorageValue
      },
      version: 1,
      migrate: (persistedState: any, version) => {
        if (version === 0) {
          // Example migration logic from version 0 to 1
          // If there are any breaking changes in store structure, handle them here
          console.log('Migrating user store from version 0 to 1');
          // For instance, if a field was renamed or removed:
          // persistedState.newField = persistedState.oldField;
          // delete persistedState.oldField;
        }
        return persistedState;
      },
    }
  )
);

export default useUserStore;