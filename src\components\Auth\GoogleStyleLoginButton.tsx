import React, { useState } from 'react';
import {
  Box,
  Button,
  Text,
  HStack,
  VStack,
  Avatar,
  useColorMode,
  useToast,
  Fade,
  Divider
} from '@chakra-ui/react';
import { useGoogleLogin } from '@react-oauth/google';
// import { useAuth } from '../../hooks/useAuthNew'; // REMOVED - causing useAuth spam
import useUserStore from '../../store/useUserStore';

interface GoogleStyleLoginButtonProps {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  variant?: 'compact' | 'full' | 'floating';
  showWelcome?: boolean;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

// Google OAuth scopes
const GOOGLE_SCOPES = [
  'https://www.googleapis.com/auth/userinfo.profile',
  'https://www.googleapis.com/auth/userinfo.email',
  'https://www.googleapis.com/auth/drive.file',
  'https://www.googleapis.com/auth/blogger'
].join(' ');

const GoogleStyleLoginButton: React.FC<GoogleStyleLoginButtonProps> = ({
  onSuccess,
  onError,
  variant = 'full',
  showWelcome = true,
  position = 'bottom-right'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { colorMode } = useColorMode();
  // Use store directly to avoid useAuth hook spam
  const { isAuthenticated, initializeUser } = useUserStore();
  const toast = useToast();

  const isDark = colorMode === 'dark';

  // Google login hook
  const googleLogin = useGoogleLogin({
    onSuccess: async (response) => {
      console.log('=== Google Login Success ===');
      
      if (!response.access_token) {
        console.error('No access token in response');
        toast({
          title: 'Lỗi đăng nhập',
          description: 'Không nhận được token từ Google',
          status: 'error',
          duration: 3000,
          isClosable: true
        });
        onError?.(new Error('No access token'));
        return;
      }

      setIsLoading(true);
      try {
        console.log('Calling login with access token...');

        // Get user info first
        const userInfoResponse = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${response.access_token}`);
        if (!userInfoResponse.ok) {
          throw new Error('Failed to get user info');
        }
        const rawUserInfo = await userInfoResponse.json();
        console.log('Raw user info received:', rawUserInfo);

        // Map to User interface format
        const userInfo = {
          sub: rawUserInfo.id || rawUserInfo.sub,
          name: rawUserInfo.name,
          given_name: rawUserInfo.given_name,
          family_name: rawUserInfo.family_name,
          picture: rawUserInfo.picture,
          email: rawUserInfo.email,
          email_verified: rawUserInfo.verified_email || rawUserInfo.email_verified,
          locale: rawUserInfo.locale,
          id: rawUserInfo.id,
          timestamp: Date.now()
        };

        console.log('Mapped user info:', userInfo);

        // Store token and user data using store
        await useUserStore.getState().setAccessToken(response.access_token);
        await useUserStore.getState().setUser(userInfo, response.access_token);

        console.log('Login completed successfully');

        // Don't show toast here - let parent component handle it
        // toast({
        //   title: 'Đăng nhập thành công',
        //   description: 'Chào mừng bạn đến với Seikowo!',
        //   status: 'success',
        //   duration: 3000,
        //   isClosable: true
        // });

        onSuccess?.();
      } catch (error) {
        console.error('Login failed:', error);
        onError?.(error);
      } finally {
        setIsLoading(false);
      }
    },
    onError: (error) => {
      console.error('=== Google Login Error ===');
      console.error('Error details:', error);
      toast({
        title: 'Lỗi đăng nhập',
        description: 'Không thể đăng nhập bằng Google',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
      onError?.(error);
    },
    scope: GOOGLE_SCOPES,
    flow: 'implicit',
    prompt: 'consent'
  });

  // Don't show if already authenticated
  if (isAuthenticated) {
    return null;
  }

  // Get position styles for floating variant
  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: 9999,
    };

    switch (position) {
      case 'bottom-right':
        return { ...baseStyles, bottom: '20px', right: '20px' };
      case 'bottom-left':
        return { ...baseStyles, bottom: '20px', left: '20px' };
      case 'top-right':
        return { ...baseStyles, top: '20px', right: '20px' };
      case 'top-left':
        return { ...baseStyles, top: '20px', left: '20px' };
      default:
        return { ...baseStyles, bottom: '20px', right: '20px' };
    }
  };

  // Floating variant - fixed position button
  if (variant === 'floating') {
    return (
      <Fade in={true}>
        <Box
          sx={{
            ...getPositionStyles(),
            transform: 'translateY(0)',
            animation: 'floatIn 0.4s ease-out',
            '@keyframes floatIn': {
              '0%': {
                transform: 'translateY(100px) scale(0.8)',
                opacity: 0
              },
              '100%': {
                transform: 'translateY(0) scale(1)',
                opacity: 1
              }
            },
            '@keyframes pulse': {
              '0%': {
                boxShadow: '0 0 0 0 rgba(26, 115, 232, 0.4)'
              },
              '70%': {
                boxShadow: '0 0 0 10px rgba(26, 115, 232, 0)'
              },
              '100%': {
                boxShadow: '0 0 0 0 rgba(26, 115, 232, 0)'
              }
            }
          }}
          _before={{
            content: '""',
            position: 'absolute',
            top: '-8px',
            left: '-8px',
            right: '-8px',
            bottom: '-8px',
            bg: 'transparent',
            borderRadius: '50%',
            zIndex: -1,
            animation: 'pulse 2s infinite',
          }}
        >
          <Button
            onClick={() => googleLogin()}
            isLoading={isLoading}
            loadingText=""
            size="lg"
            h="56px"
            w="56px"
            bg={isDark ? '#1a73e8' : '#1a73e8'}
            color="white"
            borderRadius="50%"
            boxShadow="0 4px 16px rgba(26, 115, 232, 0.4), 0 2px 8px rgba(0,0,0,0.15)"
            _hover={{
              bg: isDark ? '#1557b0' : '#1557b0',
              transform: 'scale(1.05)',
              boxShadow: '0 6px 20px rgba(26, 115, 232, 0.5), 0 4px 12px rgba(0,0,0,0.2)'
            }}
            _active={{
              transform: 'scale(0.95)',
              boxShadow: '0 2px 8px rgba(26, 115, 232, 0.3)'
            }}
            transition="all 0.2s ease"
            p={0}
            minW="56px"
          >
            {!isLoading && (
              <Box
                w="24px"
                h="24px"
                bg="linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335)"
                borderRadius="4px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Text fontSize="12px" fontWeight="bold" color="white">
                  G
                </Text>
              </Box>
            )}
          </Button>
        </Box>
      </Fade>
    );
  }

  // Compact variant - just the button
  if (variant === 'compact') {
    return (
      <Button
        onClick={() => googleLogin()}
        isLoading={isLoading}
        loadingText="Đang đăng nhập..."
        size="lg"
        h="48px"
        bg={isDark ? '#1a1a1b' : '#ffffff'}
        border="1px solid"
        borderColor={isDark ? '#343536' : '#dadce0'}
        color={isDark ? '#d7dadc' : '#3c4043'}
        _hover={{
          bg: isDark ? '#272729' : '#f8f9fa',
          borderColor: isDark ? '#4f5051' : '#dadce0',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}
        _active={{
          bg: isDark ? '#1a1a1b' : '#f1f3f4'
        }}
        fontWeight="500"
        fontSize="14px"
        borderRadius="24px"
        px={6}
        leftIcon={
          <Box
            w="18px"
            h="18px"
            bg="linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335)"
            borderRadius="3px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text fontSize="10px" fontWeight="bold" color="white">
              G
            </Text>
          </Box>
        }
      >
        Đăng nhập bằng Google
      </Button>
    );
  }

  // Full variant - Google One Tap style card
  return (
    <Fade in={true}>
      <Box
        maxW="380px"
        bg={isDark ? '#1a1a1b' : '#ffffff'}
        border="1px solid"
        borderColor={isDark ? '#343536' : '#e8eaed'}
        borderRadius="12px"
        boxShadow={isDark
          ? "0 8px 32px rgba(0,0,0,0.4), 0 2px 8px rgba(0,0,0,0.2)"
          : "0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08)"
        }
        p={6}
        mx="auto"
        backdropFilter="blur(16px)"
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: isDark ? 'rgba(26,26,27,0.95)' : 'rgba(255,255,255,0.95)',
          borderRadius: '12px',
          zIndex: -1
        }}
      >
        <VStack spacing={5} align="stretch">
          {/* Header */}
          <VStack spacing={3}>
            <HStack spacing={3} justify="center">
              <Box
                w="32px"
                h="32px"
                bg="linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335)"
                borderRadius="8px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Text fontSize="16px" fontWeight="bold" color="white">
                  G
                </Text>
              </Box>
              <Text
                fontSize="18px"
                fontWeight="600"
                color={isDark ? '#d7dadc' : '#1f1f1f'}
              >
                Đăng nhập với Google
              </Text>
            </HStack>

            {showWelcome && (
              <Text
                fontSize="14px"
                color={isDark ? '#818384' : '#5f6368'}
                textAlign="center"
                lineHeight="1.4"
              >
                Truy cập đầy đủ tính năng Blogger và quản lý nội dung của bạn
              </Text>
            )}
          </VStack>

          <Divider borderColor={isDark ? '#343536' : '#e8eaed'} />

          {/* Login Button */}
          <Button
            onClick={() => googleLogin()}
            isLoading={isLoading}
            loadingText="Đang đăng nhập..."
            w="100%"
            h="48px"
            bg={isDark ? '#1a73e8' : '#1a73e8'}
            color="white"
            borderRadius="24px"
            fontSize="14px"
            fontWeight="600"
            _hover={{
              bg: isDark ? '#1557b0' : '#1557b0',
              transform: 'translateY(-1px)',
              boxShadow: '0 4px 12px rgba(26, 115, 232, 0.4)'
            }}
            _active={{
              transform: 'translateY(0)',
              boxShadow: '0 2px 8px rgba(26, 115, 232, 0.3)'
            }}
            transition="all 0.2s ease"
          >
            Tiếp tục với Google
          </Button>

          {/* Footer */}
          <Text
            fontSize="11px"
            color={isDark ? '#818384' : '#5f6368'}
            textAlign="center"
            lineHeight="1.3"
          >
            Bằng cách tiếp tục, bạn đồng ý với{' '}
            <Text as="span" color={isDark ? '#8ab4f8' : '#1a73e8'} cursor="pointer">
              Điều khoản dịch vụ
            </Text>{' '}
            và{' '}
            <Text as="span" color={isDark ? '#8ab4f8' : '#1a73e8'} cursor="pointer">
              Chính sách quyền riêng tư
            </Text>{' '}
            của chúng tôi
          </Text>
        </VStack>
      </Box>
    </Fade>
  );
};

export default GoogleStyleLoginButton;
