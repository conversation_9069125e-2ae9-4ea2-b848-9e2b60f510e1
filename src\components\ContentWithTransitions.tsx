import React, { memo, Suspense, lazy } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { Box, Spinner, Center } from '@chakra-ui/react';
import PageTransition from './PageTransition';


// Lazy load all page components
const HomePage = lazy(() => import('../pages/HomePage'));
const PostPage = lazy(() => import('../pages/PostPage'));
const TagPage = lazy(() => import('../pages/TagPage'));

const CategoriesPage = lazy(() => import('../pages/CategoriesPage'));
const AboutPage = lazy(() => import('../pages/AboutPage'));
const ContactPage = lazy(() => import('../pages/ContactPage'));

const PrivacyPolicy = lazy(() => import('../pages/PrivacyPolicy'));
const TermsOfService = lazy(() => import('../pages/TermsOfService'));
const LoginDebug = lazy(() => import('../components/Auth/LoginDebug'));
const NotFoundPage = lazy(() => import('../pages/NotFoundPage'));
const ProtectedRoute = lazy(() => import('../components/Auth/ProtectedRoute'));

// New sidebar pages
const RemixPage = lazy(() => import('../pages/RemixPage'));
const DonatePage = lazy(() => import('../pages/DonatePage'));
const UpdatesPage = lazy(() => import('../pages/UpdatesPage'));
const UserPage = lazy(() => import('../pages/UserPage'));

// Settings page (with internal router)
const SettingsPage = lazy(() => import('../pages/SettingsPage'));
const NavigationTest = lazy(() => import('../components/Nav/NavigationTest'));
const PostPageWrapper = lazy(() => import('../components/PostPageWrapper'));
const PostEditPage = lazy(() => import('../pages/PostEditPage'));
const SearchPage = lazy(() => import('../pages/SearchPage'));
const LoginDemo = lazy(() => import('../pages/LoginDemo'));


interface PageLoaderProps { }

const PageLoader: React.FC<PageLoaderProps> = () => (
  <Center h="50vh">
    <Spinner size="xl" color="blue.500" thickness="4px" />
  </Center>
);

interface StaticPageProps {
  children: React.ReactNode;
}

// Memoize static pages
const StaticPage: React.FC<StaticPageProps> = memo(({ children }) => (
  <div className="container py-5">
    {children}
  </div>
));

StaticPage.displayName = 'StaticPage';

const ContentWithTransitions: React.FC = () => {
  const location = useLocation();

  return (
    <Box
      minH="calc(100vh - 5rem)"
      ml={{ base: 0, lg: '80px' }}
      mb={{ base: '70px', lg: 0 }}
    >
      <Suspense fallback={<PageLoader />}>
        <Routes location={location} key={location.pathname}>
          <Route path="/" element={
            <PageTransition variant="default">
              <HomePage />
            </PageTransition>
          } />
          <Route path="/tag/:tagName" element={
            <PageTransition variant="subtle">
              <TagPage />
            </PageTransition>
          } />

          <Route path="/categories" element={
            <PageTransition variant="subtle">
              <CategoriesPage />
            </PageTransition>
          } />
          <Route path="/about" element={
            <PageTransition variant="subtle">
              <AboutPage />
            </PageTransition>
          } />
          <Route path="/contact" element={
            <PageTransition variant="subtle">
              <ContactPage />
            </PageTransition>
          } />


          <Route path="/privacy-policy" element={
            <PageTransition>
              <PrivacyPolicy />
            </PageTransition>
          } />
          <Route path="/terms-of-service" element={
            <PageTransition>
              <TermsOfService />
            </PageTransition>
          } />
          <Route path="/debug-login" element={
            <PageTransition>
              <LoginDebug />
            </PageTransition>
          } />
          <Route path="/login-demo" element={
            <PageTransition variant="subtle">
              <LoginDemo />
            </PageTransition>
          } />
          <Route path="/search" element={
            <PageTransition variant="subtle">
              <SearchPage />
            </PageTransition>
          } />
          <Route path="/user" element={
            <PageTransition variant="subtle">
              <UserPage />
            </PageTransition>
          } />

          {/* Sidebar navigation routes */}
          <Route path="/remix" element={
            <PageTransition variant="default">
              <RemixPage />
            </PageTransition>
          } />
          {/* Settings Route (with internal router) */}
          <Route path="/settings/*" element={
            <PageTransition variant="subtle">
              <SettingsPage />
            </PageTransition>
          } />
          <Route path="/donate" element={
            <PageTransition variant="subtle">
              <DonatePage />
            </PageTransition>
          } />
          <Route path="/updates" element={
            <PageTransition variant="subtle">
              <UpdatesPage />
            </PageTransition>
          } />
          <Route path="/nav-test" element={
            <PageTransition variant="subtle">
              <NavigationTest />
            </PageTransition>
          } />
          <Route path="/post/edit/:postId" element={
            <PageTransition variant="modal">
              <ProtectedRoute>
                <PostEditPage />
              </ProtectedRoute>
            </PageTransition>
          } />

          {/* Blog post routes with proper params */}
          <Route path="/:year/:month/:slug" element={
            <PageTransition variant="default">
              <PostPage />
            </PageTransition>
          } />
          <Route path="/:year/:month/:slug.html" element={
            <PageTransition variant="default">
              <PostPageWrapper />
            </PageTransition>
          } />
          <Route path="/:slug.html" element={
            <PageTransition variant="default">
              <PostPageWrapper />
            </PageTransition>
          } />

          {/* 404 route for invalid post slugs */}
          <Route path="/404" element={
            <PageTransition variant="default">
              <NotFoundPage />
            </PageTransition>
          } />

          {/* Catch-all route for 404 */}
          <Route path="*" element={
            <PageTransition variant="default">
              <NotFoundPage />
            </PageTransition>
          } />
        </Routes>
      </Suspense>
    </Box>
  );
};

export default memo(ContentWithTransitions);