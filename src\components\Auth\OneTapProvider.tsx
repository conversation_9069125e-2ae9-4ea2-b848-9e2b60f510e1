import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuthNew';
import GoogleOneTap from './GoogleOneTap';
import OneTapFallback from './OneTapFallback';

interface OneTapContextType {
  isOneTapEnabled: boolean;
  disableOneTap: () => void;
  enableOneTap: () => void;
  isOneTapShown: boolean;
}

const OneTapContext = createContext<OneTapContextType | undefined>(undefined);

export const useOneTap = () => {
  const context = useContext(OneTapContext);
  if (!context) {
    throw new Error('useOneTap must be used within OneTapProvider');
  }
  return context;
};

interface OneTapProviderProps {
  children: React.ReactNode;
}

export const OneTapProvider: React.FC<OneTapProviderProps> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [isOneTapEnabled, setIsOneTapEnabled] = useState(true);
  const [isOneTapShown, setIsOneTapShown] = useState(false);
  const [showFallback, setShowFallback] = useState(false);
  const [fallbackReason, setFallbackReason] = useState<string>('');

  // Check if we're on localhost
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Disable One Tap when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setIsOneTapEnabled(false);
      setIsOneTapShown(false);
    }
  }, [isAuthenticated]);

  // Auto-enable One Tap after some time if user is not authenticated
  useEffect(() => {
    if (!isAuthenticated && !isOneTapEnabled) {
      const timer = setTimeout(() => {
        setIsOneTapEnabled(true);
      }, 60000); // Re-enable after 1 minute (less aggressive)

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isOneTapEnabled]);

  // Listen for regular login trigger
  useEffect(() => {
    const handleRegularLogin = () => {
      setShowFallback(false);
      setIsOneTapEnabled(false);
      // Trigger regular login flow
      window.dispatchEvent(new CustomEvent('openLoginModal'));
    };

    window.addEventListener('triggerRegularLogin', handleRegularLogin);
    return () => window.removeEventListener('triggerRegularLogin', handleRegularLogin);
  }, []);

  const disableOneTap = () => {
    setIsOneTapEnabled(false);
    setIsOneTapShown(false);
  };

  const enableOneTap = () => {
    if (!isAuthenticated) {
      setIsOneTapEnabled(true);
    }
  };

  const contextValue: OneTapContextType = {
    isOneTapEnabled,
    disableOneTap,
    enableOneTap,
    isOneTapShown,
  };

  return (
    <OneTapContext.Provider value={contextValue}>
      {/* One Tap temporarily disabled - too many conflicts */}
      {/* TODO: Re-implement with simpler logic */}

      {children}
    </OneTapContext.Provider>
  );
};

export default OneTapProvider;
